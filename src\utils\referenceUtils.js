/**
 * Centralized utilities for reference processing
 */
// Removed unused import

/**
 * Creates reference objects from raw terms
 * @param {Array} terms - Array of raw reference strings
 * @returns {Array} - Processed reference objects
 */
export const createReferenceObjects = (terms) => {
  if (!terms || !Array.isArray(terms)) return [];

  // Step 1: Clean the term and prepare reference objects
  const refs = terms.map((term, ind) => {
    const cleaned = term.replace(/^\d+\.\s*/, "").trim();
    const normalized = removeDOIUrl(cleaned).toLowerCase();
    return { term: cleaned, ind, _normalized: normalized };
  });

  // Step 2: Count occurrences of normalized terms
  const countMap = {};
  refs.forEach((ref) => {
    countMap[ref._normalized] = (countMap[ref._normalized] || 0) + 1;
  });

  // Step 3: Add type="DUPLICATE" if needed and remove helper field
  return refs.map(({ _normalized, ...ref }) => {
    if (countMap[_normalized] > 1) {
      return { ...ref, MarkType: "DUPLICATE" };
    }
    return ref;
  });
};

/**
 * Removes DOI URL from a single reference string
 * @param {string} reference - Reference string
 * @returns {string} - Reference with DOI URL removed
 */
export const removeDOIUrl = (reference) => {
  if (!reference) return '';
  return reference.replace(/\s*https?:\/\/(?:dx\.)?doi\.org\/\S+$/i, "").trim();
};

/**
 * Sorts and merges two arrays of references based on index
 * @param {Array} array1 - First array of references
 * @param {Array} array2 - Second array of references
 * @returns {Array} - Sorted and merged array
 */
export const sortTwoArray = (array1, array2) => {
  if (!array1 || !Array.isArray(array1)) return array2 || [];
  if (!array2 || !Array.isArray(array2)) return array1 || [];

  const combinedArray = [...array1, ...array2];
  return combinedArray.sort((a, b) => a.ind - b.ind);
};

/**
 * Sorts multiple arrays of references
 * @param {Array} arrays - Arrays of references to sort
 * @returns {Array} - Sorted array
 */
export const sortMultipleArrays = (...arrays) => {
  const combinedArray = arrays.flat().filter(Boolean);
  return combinedArray.sort((a, b) => a.ind - b.ind);
};

/**
 * Handles duplicate references by removing duplicates
 * @param {Array} references - Array of references
 * @returns {Array} - Array with duplicates removed
 */
export const handleDuplicateReferences = (references) => {
  if (!references || !Array.isArray(references)) return [];

  const uniqueRefs = [];
  const refMap = new Map();

  references.forEach((ref) => {
    const key = `${ref.authors || ''}-${ref.title || ''}-${ref.source || ''}`;
    if (!refMap.has(key)) {
      refMap.set(key, ref);
      uniqueRefs.push(ref);
    }
  });

  return uniqueRefs;
};

/**
 * Handles missing citations
 * @param {Object} ref - Reference object
 * @returns {string} - Error message if citation is missing
 */
export const handleMissingCitation = (ref) => {
  if (!ref) return '';

  if (!ref.cited) {
    return `Reference no. ${ref.ind + 1} has not been cited. Kindly cite.`;
  }
  return "";
};

/**
 * Creates initial state for reference data
 * @returns {Object} - Initial state object
 */
export const createInitialState = () => ({
  found: [],
  notFound: [],
  urlFound: [],
  multipleFound: [],
  progress: 0
});

/**
 * Creates final state for reference data
 * @param {Object} data - Reference data
 * @returns {Object} - Final state object
 */
export const createFinalState = (data) => ({
  ...data,
  progress: 100
});

/**
 * Updates progress state
 * @param {number} current - Current progress
 * @param {number} total - Total items
 * @param {number} step - Step size
 * @returns {number} - Updated progress percentage
 */
export const updateProgress = (current, total, step = 1) => {
  return Math.min(Math.round((current + step) * 100 / total), 100);
};
