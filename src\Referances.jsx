import React, { useState } from "react";
import axios from "axios";
import { upload } from "../src/constants/urls";
import { ErrorNotification } from "./components/common";
const Referances = ({ setReferences }) => {
  const [file, setFile] = useState(null);
  const [isError, setIsError] = useState("");
  const [localReferences, setLocalReferences] = useState([]);
  const handleFileChange = (e) => {
    setFile(e.target.files[0]);
  };

  const handleUpload = async () => {
    if (!file) {
      setIsError("Please select a file first!");
      return;
    }

    const formData = new FormData();
    formData.append("file", file);

    setReferences({ isLoading: 1, data: [] });
    setIsError("");
    try {
      const response = await axios.post(upload, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
          "Access-Control-Allow-Origin": "*",
        },
      });
      setReferences({ isLoading: 2, data: response.data.references });
    } catch (error) {
      console.error("Error uploading the file:", error);
    }
  };

  const handleAreaUpload = async () => {
    if(localReferences.length === 0){
      setIsError("Please enter references or seclect file!");
      return;
    }
    setReferences({ isLoading: 2, data: localReferences });
  };

  function splitReferences(text) {
    return text.split(/\n+/).filter(Boolean).map((ref, index) => `${index + 1}. ${ref.trim()}`);
}


  const handleAreaChange = (e) => {
    setLocalReferences(splitReferences(e.target.value.trim()));
  };
  return (
    <>
      <div className="upload">
        <div className="upload-container">

          <h1 className="upload-title">Upload .docx File</h1>
          <div className="upload-input-container">
          <input
            className="upload-input"
            type="file"
            onChange={handleFileChange}
            accept=".docx"
          />
          </div>
          <button className="upload-button" onClick={handleUpload}>
            Upload File
          </button>
        </div>
        <div className="upload-description">
          <h1 className="upload-title">Upload References</h1>
          <textarea
            onChange={handleAreaChange}
            className="upload-area"
          />
          <button className="upload-button" onClick={handleAreaUpload}>
            Upload References
          </button>

        </div>
      </div>
      {isError && <ErrorNotification message={isError} />}
    </>
  );
};

export default Referances;
