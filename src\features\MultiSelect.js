const options = [
  "Authors",
  "Title",
  "Journal Name",
  "Year",
  "Page",
  "Mismatch",
  "URL",
  "NotFound",
  "Duplicate"
];

const MultiSelectCheckbox = ({
  handleCheckboxChange,
  selectedOptions,
  elem,
}) => {
  return (
    <div>
      {options.map((option) => (
        <div key={option}>
          <input
            type="checkbox"
            value={option}
            checked={selectedOptions?.includes(option)}
            onChange={() => handleCheckboxChange(option, elem)}
          />
          {option}
        </div>
      ))}
    </div>
  );
};

export default MultiSelectCheckbox;
