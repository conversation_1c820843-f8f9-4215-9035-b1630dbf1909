import React from 'react';
import '../../App.css';

/**
 * Component to display error notifications
 * @param {Object} props - Component props
 * @param {string} props.message - Error message to display
 * @param {string} props.title - Optional title (defaults to "Error")
 * @returns {JSX.Element} - Rendered component
 */
const ErrorNotification = ({ message, title = "Error" }) => {
  return (
    <div className="not-found-container">
      <div className="not-found-card">
        <h2 className="not-found-title">{title}</h2>
        <p className="not-found-text">
          {message || 'An error has occurred. Please try again later.'}
        </p>
      </div>
    </div>
  );
};

export default ErrorNotification;
