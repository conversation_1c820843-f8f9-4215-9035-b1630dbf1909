import { api<PERSON>ey, db, retmax, retmode } from "../../constants/env";
import { esearch, esummary, crossRef, nlmCatalog, nlmCatalogSummary } from "../../constants/urls";
import { delayedFetch } from "../utils/fetchUtils";

export class ApiService {
  static async searchPubMed(term) {
    const encodedTerm = encodeURI(term.trim().replace(/\s+/g, "+").replace("&", ""));
    const searchUrl = `${esearch}?db=${db}&term=${encodedTerm}&retmode=${retmode}&retmax=${retmax}&api_key=${apiKey}`;
    
    const searchResponse = await delayedFetch(searchUrl);
    if (!searchResponse.ok) throw new Error("Search API failed");
    
    const searchResult = await searchResponse.json();
    if (!searchResult.esearchresult.idlist.length) return null;

    const pmids = searchResult.esearchresult.idlist.join(",");
    const summaryUrl = `${esummary}?db=${db}&id=${pmids}&retmode=${retmode}&api_key=${apiKey}`;

    const summaryResponse = await delayedFetch(summaryUrl);
    if (!summaryResponse.ok) throw new Error("Summary API failed");

    return await summaryResponse.json();
  }

  static async searchCrossRef(term) {
    const url = `${crossRef}?query=${encodeURIComponent(term)}&rows=1`;
    const response = await delayedFetch(url);
    if (!response.ok) throw new Error("CrossRef API failed");
    return await response.json();
  }

  static async getJournalInfo(searchTerm, allFields = false) {
    const params = !allFields ? `[Journal]AND%22english%22[Language]` : ``;
    const searchUrl = `${nlmCatalog}${encodeURIComponent(searchTerm)}${params}&api_key=${apiKey}`;

    const searchResponse = await delayedFetch(searchUrl);
    const searchData = await searchResponse.json();

    if (!searchData.esearchresult.idlist.length) {
      return null;
    }

    const id = searchData.esearchresult.idlist[0];
    const summaryUrl = `${nlmCatalogSummary}${id}&api_key=${apiKey}`;
    const summaryResponse = await delayedFetch(summaryUrl);
    const summaryData = await summaryResponse.json();

    return summaryData.result[id];
  }
}