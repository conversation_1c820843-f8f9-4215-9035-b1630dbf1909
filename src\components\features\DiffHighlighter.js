import { diffChars } from "diff";
 
const DiffHighlighter = ({ text1, text2 }) => {
  const diff = diffChars(text1, text2);

  return (
    <p id="display" className="word-wrap break-words">
      {diff.map((part, index) => (
        <span
          key={index}
          style={{ color: part.added ? "green" : part.removed ? "red" : "grey" }}
        >
          {part.value}
        </span>
      ))}
    </p>
  );
};

export default DiffHighlighter;
