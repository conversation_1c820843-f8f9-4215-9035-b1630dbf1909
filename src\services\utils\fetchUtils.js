import {
  extract,
  nlmCatalog,
  nlmCatalogSummary,
} from "../../constants/urls.js";
import { delayedFetch, abbreviatePageNumbers } from "../styling.js";
import { searchJournalInGenAI } from "../fetchFromGenAI";

import { formatText } from "./formatUtils.js";

export async function extractDetailsFromAPI(content, Prompt) {
  try {
    // Fetch data from the API
    const response = await delayedFetch(extract, {
      method: "POST",
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ content: `${Prompt(content)}` }),
    });
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    // Parse the JSON response
    const data = await response.json();
    // Extract citation details
    return data;
  } catch (error) {
    console.error("Error fetching CrossRef data:", error);
    return null;
  }
}

export const extractFormattedData = async (extractedData, formatJournal) => {
  const article_title = formatText(extractedData?.article_title);
  const authors = extractedData?.authors;
  const year = extractedData?.year;
  const journal_title = formatJournal
    ? await getJournalNameWithFallback(extractedData?.journal_title)
    : extractedData?.journal_title;
  const pages = abbreviatePageNumbers(extractedData?.pages);
  const volume = extractedData?.volume;
  return {
    finalStr: `${authors} ${article_title} ${journal_title} ${
      year ? year + ";" : ""
    }${volume ? volume : ""}${pages ? ":" + pages : ""}.`,
    extractedData,
    searchStr: `${article_title}[Title] ${journal_title}[Journal] ${authors
      .replaceAll("et al", "")
      .split(", ")
      .join("[Author] ")}[Author] ${year}`,
      searchStr2: `${article_title}[Title] ${journal_title}[Journal] ${year}`,
  };
};

function titleSimilarityCheck(title1, title2) {
  if (!title1 || !title2) return false;

  const normalize = (str) => {
    const stopwords = new Set([
      "the",
      "and",
      "is",
      "a",
      "an",
      "on",
      "in",
      "at",
    ]);
    return str
      .toLowerCase()
      .normalize("NFC") // Normalize Unicode
      .replace(/[^\w\s]/g, "")
      .split(/\s+/)
      .filter((word) => word.length > 2 && !stopwords.has(word));
  };

  const words1 = new Set(normalize(title1));
  const words2 = new Set(normalize(title2));

  const intersectionSize = [...words1].filter((word) =>
    words2.has(word)
  ).length;
  const avgSize = (words1.size + words2.size) / 2; // Use the average size instead of min

  const similarity = intersectionSize / avgSize;

  return similarity >= 0.6; // 60% threshold
}

export async function getJournalNameWithFallback(searchTerm) {
  if (!searchTerm) return "";

  let searchString = Array.isArray(searchTerm)
    ? searchTerm[0]?.replaceAll("&amp;", " and ").replaceAll("&", " and ")
    : searchTerm?.replaceAll("&amp;", " and ").replaceAll("&", " and ");
  searchString.replaceAll(".", "");
  const journal = await searchJournalInGenAI(searchString);
  if (!journal) return searchString;
  return journal;
  // try {
  // const title = journal.toLowerCase();
  // console.log("Journal title:", title);
  // console.log("Search string:", searchString);

  // const isSimilar = titleSimilarityCheck(title, searchString);
  // console.log("titleSimilarityCheck result:", isSimilar);

  // if (isSimilar) {
  //   console.log("Returning early with:", journal);
  //   return journal; // This should exit the function
  // }

  //   const journalWithAllFields = await searchJournalInGenAI(searchString);

  // console.log("Proceeding to fetch journalWithAllFields...");
  // const journalWithAllFields = await getJournalAbbreviation(
  //   searchString,
  //   true
  // );
  // if (journalWithAllFields) return journalWithAllFields;
  // if (!journalWithAllFields) return searchString;
  // if (
  //   titleSimilarityCheck(
  //     journalWithAllFields.titlemainlist[0].title,
  //     searchString
  //   )
  // ) {
  //   return journalWithAllFields.medlineta;
  // }

  // } catch (error) {
  //   console.error("Error in getJournalNameWithFallback:", error);
  //   return searchString;
  // }
}

const API_KEY = "4c5739b0da6cefd7734590674846548a8e08";

export async function getJournalAbbreviation(searchTerm, allFeilds) {
  const params = !allFeilds ? `[Journal]AND%22english%22[Language]` : ``;

  const searchUrl = `${nlmCatalog}${encodeURIComponent(
    searchTerm?.replaceAll("&", " and ")
  )}${params}&api_key=${API_KEY}&retmode=json&retmax=1`;

  try {
    const searchResponse = await delayedFetch(searchUrl);
    const searchData = await searchResponse.json();

    if (!searchData.esearchresult.idlist.length) {
      return null;
    }

    for await (const id of searchData.esearchresult.idlist) {
      const summaryUrl = `${nlmCatalogSummary}${id}&api_key=${API_KEY}&retmode=json`;
      const summaryResponse = await delayedFetch(summaryUrl);
      const summaryData = await summaryResponse.json();
      if (allFeilds && summaryData.result[id]) {
        return summaryData.result[id];
      }
      if (
        !allFeilds &&
        summaryData.result[id] &&
        (summaryData.result[id].language === "eng" ||
          summaryData.result[id].language === "eng eng")
      ) {
        return summaryData.result[id];
      }
    }
  } catch (error) {
    console.error("Error:", error);
    return null;
  }
}
