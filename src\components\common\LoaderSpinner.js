import React from 'react';
import '../../App.css';

/**
 * Component to display a loading spinner
 * @param {Object} props - Component props
 * @param {number} props.progress - Optional progress percentage
 * @returns {JSX.Element} - Rendered component
 */
const LoaderSpinner = ({ progress }) => {
  const showProgress = typeof progress === 'number';
  
  return (
    <div className="fixed-overlay">
      <div className="loader-container">
        <div className="loader"></div>
        {showProgress && (
          <div className="progress-text">{progress}%</div>
        )}
      </div>
    </div>
  );
};

export default LoaderSpinner;
