import { ApiService } from '../api/apiService';
import { formatCitation } from './formatUtils';

export class ReferenceService {
  static async searchReferences(references) {
    try {
      const results = await Promise.all(
        references.map(async (ref) => {
          const found = await ApiService.searchPubMed(ref.term);
          const isMultipleArticles = found?.result?.uids?.length > 1;
          const extractedData = formatCitation(found?.result?.[found?.result?.uids?.[0]]);

          if (found && extractedData) {
            return { type: "found", data: { ...ref, ...extractedData, type: "PubMed", found } };
          } else if (isMultipleArticles) {
            return { type: "multiple", data: { ...ref, ...extractedData, type: "PubMed", found } };
          }
          return { type: "notFound", data: ref };
        })
      );

      return results.reduce(
        (acc, curr) => {
          acc[curr.type === "found" ? "pubMedArray" : 
              curr.type === "multiple" ? "multipleFound" : "notFoundArray"].push(curr.data);
          return acc;
        },
        { pubMedArray: [], notFoundArray: [], multipleFound: [] }
      );
    } catch (error) {
      console.error("Error in searchReferences:", error);
      throw error;
    }
  }
}