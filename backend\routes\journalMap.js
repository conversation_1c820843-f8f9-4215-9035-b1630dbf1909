const express = require('express');
const fs = require('fs');
const path = require('path');
const router = express.Router();

// Path to the journalMap.json file
const JOURNAL_MAP_PATH = path.join(__dirname, '../../src/services/helpers/journalMap.json');

/**
 * POST /api/update-journal-map
 * Updates the journalMap.json file with new journal mappings
 */
router.post('/update-journal-map', async (req, res) => {
  try {
    const { originalJournal, abbreviatedJournal } = req.body;
    
    // Validate input
    if (!originalJournal || !abbreviatedJournal) {
      return res.status(400).json({
        success: false,
        error: 'Both originalJournal and abbreviatedJournal are required'
      });
    }
    
    console.log(`📡 API Request: Add "${originalJournal}" -> "${abbreviatedJournal}"`);
    
    // Read current journalMap.json
    let currentMap = {};
    try {
      if (fs.existsSync(JOURNAL_MAP_PATH)) {
        const fileContent = fs.readFileSync(JOURNAL_MAP_PATH, 'utf8');
        currentMap = JSON.parse(fileContent);
      }
    } catch (parseError) {
      console.error('Error reading journalMap.json:', parseError);
      return res.status(500).json({
        success: false,
        error: 'Failed to read existing journal map'
      });
    }
    
    // Check if mapping already exists
    if (currentMap[originalJournal]) {
      console.log(`✅ Mapping already exists: "${originalJournal}" -> "${currentMap[originalJournal]}"`);
      return res.json({
        success: true,
        message: 'Mapping already exists',
        existing: currentMap[originalJournal],
        totalMappings: Object.keys(currentMap).length
      });
    }
    
    // Add new mapping
    currentMap[originalJournal] = abbreviatedJournal;
    
    // Sort the map alphabetically for better organization
    const sortedMap = {};
    Object.keys(currentMap)
      .sort()
      .forEach(key => {
        sortedMap[key] = currentMap[key];
      });
    
    // Write back to file with proper formatting
    try {
      fs.writeFileSync(JOURNAL_MAP_PATH, JSON.stringify(sortedMap, null, 2), 'utf8');
      console.log(`📝 Successfully added to journalMap.json: "${originalJournal}" -> "${abbreviatedJournal}"`);
    } catch (writeError) {
      console.error('Error writing to journalMap.json:', writeError);
      return res.status(500).json({
        success: false,
        error: 'Failed to write to journal map file'
      });
    }
    
    // Success response
    res.json({
      success: true,
      message: 'Journal mapping added successfully',
      added: {
        originalJournal,
        abbreviatedJournal
      },
      totalMappings: Object.keys(sortedMap).length
    });
    
  } catch (error) {
    console.error('Error in update-journal-map endpoint:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/journal-map
 * Returns the current journal map
 */
router.get('/journal-map', (req, res) => {
  try {
    if (!fs.existsSync(JOURNAL_MAP_PATH)) {
      return res.json({
        success: true,
        data: {},
        totalMappings: 0
      });
    }
    
    const fileContent = fs.readFileSync(JOURNAL_MAP_PATH, 'utf8');
    const journalMap = JSON.parse(fileContent);
    
    res.json({
      success: true,
      data: journalMap,
      totalMappings: Object.keys(journalMap).length
    });
    
  } catch (error) {
    console.error('Error reading journal map:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * DELETE /api/journal-map/:journal
 * Removes a journal mapping
 */
router.delete('/journal-map/:journal', (req, res) => {
  try {
    const journalToDelete = req.params.journal;
    
    if (!fs.existsSync(JOURNAL_MAP_PATH)) {
      return res.status(404).json({
        success: false,
        error: 'Journal map file not found'
      });
    }
    
    const fileContent = fs.readFileSync(JOURNAL_MAP_PATH, 'utf8');
    const currentMap = JSON.parse(fileContent);
    
    if (!currentMap[journalToDelete]) {
      return res.status(404).json({
        success: false,
        error: 'Journal mapping not found'
      });
    }
    
    delete currentMap[journalToDelete];
    
    fs.writeFileSync(JOURNAL_MAP_PATH, JSON.stringify(currentMap, null, 2), 'utf8');
    
    res.json({
      success: true,
      message: 'Journal mapping deleted successfully',
      deleted: journalToDelete,
      totalMappings: Object.keys(currentMap).length
    });
    
  } catch (error) {
    console.error('Error deleting journal mapping:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
