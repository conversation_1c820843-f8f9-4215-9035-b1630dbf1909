import {
  extractDetailsFromAPI,
  extractFormattedData,
} from "../components/utils/util";
import Prompt from "../constants/prompt";
import JournalPrompt from "../constants/getJournalName";
import journalMap from "../services/helpers/journalMap.json";
import { updateJournalMapping } from "../components/utils/util";

const BATCH_SIZE = 3;

const createBatches = (array, size) => {
  const batches = [];
  for (let i = 0; i < array.length; i += size) {
    batches.push(array.slice(i, i + size));
  }
  return batches;
};

export const searchInGenAI = async (references, formatJournal) => {
  const genAIArray = [];

  if (formatJournal) {
    for (const ref of references) {
      const formattedData = await extractFormattedData(
        ref.extractedData,
        formatJournal
      );
      genAIArray.push({ ...ref, ...formattedData });
    }

    return { genAIArray };
  } else {
    const batches = createBatches(references, BATCH_SIZE);
    for (const batch of batches) {
      const structuredDataArray = await extractDetailsFromAPI(batch, Prompt);
      if (!structuredDataArray?.entries) {
        genAIArray.push({ ...batch });
      }
      for (
        let index = 0;
        index < structuredDataArray?.entries?.length;
        index++
      ) {
        const element = structuredDataArray.entries[index];
        const formattedData = await extractFormattedData({
          ...element,
          original_term: batch[index].term // Pass the original term
        }, true); // Skip journal lookup for PubMed - we'll get journal from PubMed response
        genAIArray.push({ ...batch[index], ...formattedData });
      }
    }

    return { genAIArray };
  }
};

export const searchJournalInGenAI = async (journal_title) => {
  const cleanedTitle = Array.isArray(journal_title)
    ? journal_title[0]
    : journal_title;
  const trimedTitle = cleanedTitle.replaceAll("The", "").trim();

  // Check journalMap.json first
  if (journalMap[trimedTitle]) {
    console.log(`Using journalMap.json: "${trimedTitle}" -> "${journalMap[trimedTitle]}"`);
    return journalMap[trimedTitle];
  } else {
    // Fallback to GenAI
    const data = await extractDetailsFromAPI(cleanedTitle, JournalPrompt);
    console.log(cleanedTitle, "JournalTitle from GenAI:", data);

    // Cache the GenAI result for future use
    if (data?.journal_title && data.journal_title !== cleanedTitle) {
      updateJournalMapping(cleanedTitle, data.journal_title);
      console.log(`Cached new journal mapping from GenAI: "${cleanedTitle}" -> "${data.journal_title}"`);
    }

    return data?.journal_title;
  }
};
