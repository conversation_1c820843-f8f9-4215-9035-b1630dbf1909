const Prompt = (contentArray) => {
  if (!Array.isArray(contentArray)) {
    throw new Error("Invalid input: contentArray must be an array");
  }

  const sanitizedEntries = contentArray.map((entry, index) => ({
    entryNumber: index + 1,
    text: (entry.genTerm ?? "").trim(),
  }));

  const promptData = {
    instruction: `Extract the following details from each entry in the given array of text:
 1. Authors: <AUTHORS>
 2. Article Title: Extract the full title of the article.
 3. Journal Title: Extract the full name of the journal.
 4. Volume: Extract the volume number or supplement no if available.
 5. Year: Extract the year of publication.
 6. Page/Page Range: Extract the page number(s) as provided.
 
 Expected output format:
 {
   "entries": [
     {
       "authors": "string",
       "article_title": "string",
       "journal_title": "string",
       "volume": "string",
       "year": "string",
       "pages": "string"
     }
   ]
 }`,
    text_entries: sanitizedEntries,
    output_format: "JSON",
  };

  return JSON.stringify(promptData, null, 2);
};

export default Prompt;
