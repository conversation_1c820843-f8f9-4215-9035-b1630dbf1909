export const sortTwoArray = (arr1, arr2) => {
  return [...arr1, ...arr2].sort((a, b) => a.ind - b.ind);
};

export const sortMultipleArrays = (...arrays) => {
  return arrays.flat().sort((a, b) => a.ind - b.ind);
};

export const removeDOIUrl = (reference) => {
  return reference.replace(/\s*https?:\/\/(?:dx\.)?doi\.org\/\S+$/i, "").trim();
};

export const createReferenceObjects = (terms) => {
  // Step 1: Clean the term and prepare reference objects
  const refs = terms.map((term, ind) => {
    const cleaned = term.replace(/^\d+\.\s*/, "").trim();
    const normalized = removeDOIUrl(cleaned).toLowerCase();
    return { term: cleaned, ind, _normalized: normalized };
  });

  // Step 2: Count occurrences of normalized terms
  const countMap = {};
  refs.forEach((ref) => {
    countMap[ref._normalized] = (countMap[ref._normalized] || 0) + 1;
  });

  // Step 3: Add type="DUPLICATE" if needed and remove helper field
  return refs.map(({ _normalized, ...ref }) => {
    if (countMap[_normalized] > 1) {
      return { ...ref, MarkType: "DUPLICATE" };
    }
    return ref;
  });
};
