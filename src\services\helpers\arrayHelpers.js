export const sortTwoArray = (arr1, arr2) => {
  return [...arr1, ...arr2].sort((a, b) => a.ind - b.ind);
};

export const sortMultipleArrays = (...arrays) => {
  return arrays.flat().sort((a, b) => a.ind - b.ind);
};

export const removeDOIUrl = (reference) => {
  return reference.replace(/\s*https?:\/\/(?:dx\.)?doi\.org\/\S+$/i, "").trim();
};

export const createReferenceObjects = (terms) => {
  // Step 1: Clean the term and prepare reference objects
  const refs = terms.map((term, ind) => {
    const cleaned = term.replace(/^\d+\.\s*/, "").trim();
    // Improved normalization for better duplicate detection
    const normalized = normalizeReferenceForDuplicateCheck(cleaned);
    return { term: cleaned, ind, _normalized: normalized };
  });

  // Step 2: Group references by normalized content
  const duplicateGroups = {};
  refs.forEach((ref) => {
    if (!duplicateGroups[ref._normalized]) {
      duplicateGroups[ref._normalized] = [];
    }
    duplicateGroups[ref._normalized].push(ref);
  });

  // Step 3: Add enhanced duplicate information
  return refs.map(({ _normalized, ...ref }) => {
    const group = duplicateGroups[_normalized];
    if (group.length > 1) {
      // Find other references in the same duplicate group
      const otherDuplicates = group
        .filter(r => r.ind !== ref.ind)
        .map(r => r.ind + 1); // Convert to 1-based indexing for display

      return {
        ...ref,
        MarkType: "DUPLICATE",
        duplicateOf: otherDuplicates,
        duplicateGroup: _normalized
      };
    }
    return ref;
  });
};

// Improved normalization function for better duplicate detection
const normalizeReferenceForDuplicateCheck = (reference) => {
  if (!reference) return '';

  return reference
    .toLowerCase()
    // Remove DOI URLs
    .replace(/\s*https?:\/\/(?:dx\.)?doi\.org\/\S+$/i, "")
    // Remove extra whitespace
    .replace(/\s+/g, " ")
    // Remove common punctuation that might vary
    .replace(/[.,;:]/g, "")
    // Remove page numbers that might vary (e.g., "123-128" vs "123-8")
    .replace(/\b\d+[-–]\d+\b/g, "")
    // Remove years in parentheses that might vary
    .replace(/\(\d{4}\)/g, "")
    // Remove volume/issue numbers that might vary
    .replace(/\b\d+\(\d+\)/g, "")
    .trim();
};
