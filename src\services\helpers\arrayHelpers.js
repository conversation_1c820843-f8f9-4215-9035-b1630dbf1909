export const sortTwoArray = (arr1, arr2) => {
  return [...arr1, ...arr2].sort((a, b) => a.ind - b.ind);
};

export const sortMultipleArrays = (...arrays) => {
  return arrays.flat().sort((a, b) => a.ind - b.ind);
};

export const removeDOIUrl = (reference) => {
  return reference.replace(/\s*https?:\/\/(?:dx\.)?doi\.org\/\S+$/i, "").trim();
};

export const createReferenceObjects = (terms) => {
  // Step 1: Clean the term and prepare reference objects
  const refs = terms.map((term, ind) => {
    const cleaned = term.replace(/^\d+\.\s*/, "").trim();
    // Improved normalization for better duplicate detection
    const normalized = normalizeReferenceForDuplicateCheck(cleaned);
    return { term: cleaned, ind, _normalized: normalized };
  });

  // Step 2: Count occurrences of normalized terms
  const countMap = {};
  refs.forEach((ref) => {
    countMap[ref._normalized] = (countMap[ref._normalized] || 0) + 1;
  });

  // Step 3: Add MarkType="DUPLICATE" if needed and remove helper field
  return refs.map(({ _normalized, ...ref }) => {
    if (countMap[_normalized] > 1) {
      return { ...ref, MarkType: "DUPLICATE" };
    }
    return ref;
  });
};

// Improved normalization function for better duplicate detection
const normalizeReferenceForDuplicateCheck = (reference) => {
  if (!reference) return '';

  return reference
    .toLowerCase()
    // Remove DOI URLs
    .replace(/\s*https?:\/\/(?:dx\.)?doi\.org\/\S+$/i, "")
    // Remove extra whitespace
    .replace(/\s+/g, " ")
    // Remove common punctuation that might vary
    .replace(/[.,;:]/g, "")
    // Remove page numbers that might vary (e.g., "123-128" vs "123-8")
    .replace(/\b\d+[-–]\d+\b/g, "")
    // Remove years in parentheses that might vary
    .replace(/\(\d{4}\)/g, "")
    // Remove volume/issue numbers that might vary
    .replace(/\b\d+\(\d+\)/g, "")
    .trim();
};
