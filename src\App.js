import React, { useState } from "react";
import PubMedComponent from "./features/PubMedComponent";
import Referances from "./Referances";
function App() {
  const [terms, setTerms] = useState({isLoading:0,data:[]});

  return (
    <>
      <Referances setReferences={setTerms} />
      {(terms.data.length > 0 || terms.isLoading>0) &&  <PubMedComponent terms={terms} /> }
    </>
  );
}

export default App;
