import React, { useState } from "react";
import PubMedComponent from "./features/PubMedComponent";
import Referances from "./Referances";
import "./App.css";

function App() {
  const [terms, setTerms] = useState({isLoading:0,data:[]});
  const [isUploadDrawerOpen, setIsUploadDrawerOpen] = useState(true);

  // Auto-collapse upload drawer when processing starts
  React.useEffect(() => {
    if (terms.isLoading > 0 || terms.data.length > 0) {
      // Auto-collapse after 1 second to show the upload was successful
      const timer = setTimeout(() => {
        setIsUploadDrawerOpen(false);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [terms.isLoading, terms.data.length]);

  return (
    <>
      {/* Collapsible Upload Drawer */}
      <div className={`upload-drawer ${isUploadDrawerOpen ? 'open' : 'collapsed'}`}>
        <div className="upload-drawer-header">
          <h2>📁 Upload References</h2>
          <button
            className="drawer-toggle-button"
            onClick={() => setIsUploadDrawerOpen(!isUploadDrawerOpen)}
          >
            {isUploadDrawerOpen ? '▲ Hide Upload' : '▼ Show Upload'}
          </button>
        </div>

        {isUploadDrawerOpen && (
          <div className="upload-drawer-content">
            <Referances setReferences={setTerms} />
          </div>
        )}
      </div>

      {/* Results Section */}
      {(terms.data.length > 0 || terms.isLoading > 0) && (
        <div className="results-section">
          {/* Quick Upload Toggle Button (when drawer is collapsed) */}
          {!isUploadDrawerOpen && (
            <div className="quick-upload-toggle">
              <button
                className="quick-toggle-button"
                onClick={() => setIsUploadDrawerOpen(true)}
                title="Upload new references"
              >
                📁 Upload New References
              </button>
            </div>
          )}

          <PubMedComponent terms={terms} />
        </div>
      )}
    </>
  );
}

export default App;
