import * as XLSX from "xlsx";
import { saveAs } from "file-saver";

const downloadExcel = (jsonData, fileName = "data.xlsx") => {
  // Convert JSON to a worksheet
  const arr=jsonData.map(item=>{
    return {
      Original: item.term,
      Updated: item.finalStr,
      Authors: <AUTHORS>
      Title: item.incorrect?.includes("Title") ? "Yes" : "No",
      "Journal Name": item.incorrect?.includes("Journal Name") ? "Yes" : "No",
      Year: item.incorrect?.includes("Year") ? "Yes" : "No",
      Page: item.incorrect?.includes("Page") ? "Yes" : "No",
      Mismatch: item.incorrect?.includes("Mismatch") ? "Yes" : "No",
      URL: item.incorrect?.includes("URL") ? "Yes" : "No",
      NotFound: item.incorrect?.includes("NotFound") ? "Yes" : "No",
      Duplicate: item.incorrect?.includes("Duplicate") ? "Yes" : "No"
    }
  });
  const worksheet = XLSX.utils.json_to_sheet(arr);

  // Create a new workbook and append the worksheet
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

  // Write workbook and convert it to a binary buffer
  const excelBuffer = XLSX.write(workbook, { bookType: "xlsx", type: "array" });

  // Create a Blob and trigger download
  const data = new Blob([excelBuffer], { type: "application/octet-stream" });
  saveAs(data, fileName);
};

export default downloadExcel;
