/**
 * Centralized API utilities for making network requests
 */

/**
 * Makes a fetch request with a delay to avoid rate limiting
 * @param {string} url - The URL to fetch
 * @param {Object} options - Fetch options
 * @returns {Promise<Response>} - The fetch response
 */
export const delayedFetch = async (url, options = {}) => {
  // Add a small delay to avoid rate limiting
  await new Promise(resolve => setTimeout(resolve, 300));
  return fetch(url, options);
};

/**
 * Removes DOI URLs from references
 * @param {Array} references - Array of reference objects
 * @returns {Array} - References with DOI URLs removed
 */
export const removeDOIUrls = (references) => {
  if (!references || !Array.isArray(references)) return [];
  
  return references.map((ref) => ({
    ...ref,
    genTerm: ref.term
      .replace(/\s*https?:\/\/(?:dx\.)?doi\.org\/\S+$/i, "")
      .trim(),
  }));
};

/**
 * Creates batches from an array
 * @param {Array} array - The array to batch
 * @param {number} size - Batch size
 * @returns {Array} - Array of batches
 */
export const createBatches = (array, size) => {
  if (!array || !Array.isArray(array)) return [];
  
  const batches = [];
  for (let i = 0; i < array.length; i += size) {
    batches.push(array.slice(i, i + size));
  }
  return batches;
};

/**
 * Extracts details from an API response
 * @param {string} content - Content to extract from
 * @param {Function} promptGenerator - Function to generate prompt
 * @returns {Promise<Object>} - Extracted data
 */
export async function extractDetailsFromAPI(content, promptGenerator) {
  if (!content || !promptGenerator) return null;
  
  try {
    const response = await fetch(process.env.REACT_APP_EXTRACT_URL || 'http://13.49.18.242:4999/extract', {  
      method: "POST",
      headers: {
        'Access-Control-Allow-Origin': '*',
        "Content-Type": 'application/json'
      },
      body: JSON.stringify({content: `${promptGenerator(content)}`})
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error("Error in API extraction:", error);
    return null;
  }
}
