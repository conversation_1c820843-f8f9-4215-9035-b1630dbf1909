import React, { useState } from "react";
import "../App.css";
import useFetchPubMedData from "../hooks/useFetchPubMedData";
import {
  Diff<PERSON><PERSON><PERSON>er,
  NotFoundReferences,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ReferenceStatistics
} from "../components/common";
import MultiSelectCheckbox from "./MultiSelect";
import downloadExcel from "./downloadExcel";

// import { terms } from "../constants/term";
import { Document, Packer, Paragraph } from "docx";
import { saveAs } from "file-saver";

const downloadDocx = async (references) => {
  const doc = new Document({
    sections: [
      {
        properties: {},
        children: references.map((ref) => {
          console.log(ref, "ref");

          return new Paragraph({
            text: `${ref.ind + 1}. ${ref.finalStr || ref.term}`,
          });
        }),
      },
    ],
  });

  const blob = await Packer.toBlob(doc);
  saveAs(blob, "references.docx");
};

const PubMedComponent = ({ terms }) => {
  const [prog, setProg] = useState(0);
  const hasTestParam = new URLSearchParams(window.location.search).has("test");
  const [isDiffViewerOpen, setIsDiffViewerOpen] = React.useState(false);
  const [showStatistics, setShowStatistics] = useState(false);
  const { data, loading, error, setData } = useFetchPubMedData(
    terms.data,
    true,
    setProg
  );
  const [rowSelections, setRowSelections] = useState({});
  const handleCheckboxChange = (option, elem) => {
    const rowId = elem.ind;
    setRowSelections((prev) => {
      const currentRowSelections = prev[rowId] || [];
      const newRowSelections = currentRowSelections.includes(option)
        ? currentRowSelections.filter((item) => item !== option)
        : [...currentRowSelections, option];
      if (data.found[rowId]) {
        data.found[rowId].incorrect = newRowSelections;
      }
      return {
        ...prev,
        [rowId]: newRowSelections,
      };
    });
  };

  const handleReferenceEdit = (index, newText) => {
    setData(prevData => ({
      ...prevData,
      found: prevData.found.map(ref =>
        ref.ind === index ? { ...ref, finalStr: newText } : ref
      )
    }));
  };

  if (terms.isLoading === 2 && terms.data.length === 0)
    return <NotFoundReferences />;
  if (error) return <ErrorNotification message={error} />;
  if (typeof data === "string") return <ErrorNotification message={data} />;
  return (
    <>
      {loading && <LoaderSpinner progress={prog} />}

      <hr />
      <div className="found-citation-wrapper">
        <h1> Found Citation</h1>
        <div className="download-wrapper">
          <div>
            <button
              className="toggle-button"
              onClick={() => {
                setIsDiffViewerOpen((pre) => !pre);
              }}
            >
              {isDiffViewerOpen ? "Hide Differences" : "Show Differences"}
            </button>
          </div>
          <div>
            <button
              className="toggle-button"
              onClick={() => downloadDocx(data.found)}
            >
              Download DOCX
            </button>
            {hasTestParam && (
              <button
                className="toggle-button"
                onClick={() => downloadExcel(data.found, "references.xlsx")}
              >
                Download Report
              </button>
            )}
            <button
              className="toggle-button"
              onClick={() => setShowStatistics(!showStatistics)}
            >
              {showStatistics ? 'Hide' : 'Show'} Statistics
            </button>
          </div>
        </div>
      </div>

      {showStatistics && (
        <ReferenceStatistics references={data.found} />
      )}

      <table className="table">
        <tr>
          <th>ID</th>
          {isDiffViewerOpen ? (
            <th>
              <strong>Citation</strong>
            </th>
          ) : (
            <>
              <th>
                <strong>term</strong>
              </th>
              <th>
                <strong>final string</strong>
              </th>
              {hasTestParam && (
                <th>
                  <strong>Choose Incorrect</strong>
                </th>
              )}
            </>
          )}
        </tr>
        {data?.found?.map((elem, ind) => (
          <DetailedBox
            elem={elem}
            key={"found" + ind}
            isDiffViewerOpen={isDiffViewerOpen}
            selectedOptions={rowSelections[elem.ind] || []}
            handleCheckboxChange={handleCheckboxChange}
            onReferenceEdit={handleReferenceEdit}
          />
        ))}
      </table>
    </>
  );
};

// Add this helper function before DetailedBox component
const copyToClipboard = (text) => {
  navigator.clipboard.writeText(text);
};

const searchInPubMed = (text) => {
  const searchTerm = encodeURIComponent(text);
  window.open(`https://pubmed.ncbi.nlm.nih.gov/?term=${searchTerm}`, "_blank");
};

// Modify the DetailedBox component's td that shows finalStr
const DetailedBox = ({
  elem,
  isDiffViewerOpen,
  handleCheckboxChange,
  selectedOptions,
  onReferenceEdit,
}) => {
  const hasTestParam = new URLSearchParams(window.location.search).has("test");
  if (typeof elem === "string") {
    return;
  }
  return (
    <tr data-type={elem.type} data-marktype={elem.MarkType}>
      <td>{elem.ind + 1}</td>
      {isDiffViewerOpen ? (
        <td>
          <DiffHighlighter
            text1={elem.term}
            text2={elem.finalStr ?? elem.term}
          />{" "}
        </td>
      ) : (
        <>
          <td>
            {elem.term}
            <button
              onClick={() => searchInPubMed(elem.term)}
              title="Search in PubMed"
              className="action-button"
            >
              🔍
            </button>
          </td>
          <td>
            <ReferenceEditor
              reference={elem}
              onSave={onReferenceEdit}
            />
            <ReferenceQualityChecker reference={elem} />
            <button
              onClick={() => copyToClipboard(elem.finalStr)}
              title="Copy reference"
              className="action-button"
            >
              📋
            </button>
          </td>
          {hasTestParam && (
            <td>
              <MultiSelectCheckbox
                {...{ elem, handleCheckboxChange, selectedOptions }}
              />
            </td>
          )}
        </>
      )}
    </tr>
  );
};

export default PubMedComponent;
