import { extract, nlmCatalog, nlmCatalogSummary } from "../../constants/urls";
import {
  delayedFetch,
  formatText,
  abbreviatePageNumbers,
} from "../../services/styling";
export async function extractDetailsFromAPI(content, Prompt) {

  try {
    // Fetch data from the API
    const response = await fetch(extract,{
      method: "POST",
      headers: {
        'Access-Control-Allow-Origin': '*',
        "Content-Type": 'application/json'
      },
      body: JSON.stringify({content: `${Prompt(content)}`})
    });
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    // Parse the JSON response
    const data = await response.json();
    // Extract citation details
    return data;
  } catch (error) {
    console.error("Error fetching CrossRef data:", error);
    return null;
  }
}

export const extractFormattedData = async (extractedData) => {
  const article_title = formatText(extractedData?.article_title);
  const authors = extractedData?.authors;
  const year = extractedData?.year;
  const journal_title = await getJournalNameWithFallback(
    extractedData?.journal_title
  );
  const pages = abbreviatePageNumbers(extractedData?.pages);
  const volume = extractedData?.volume;

  // Generate search strings for PubMed (3 strategies)
  const searchStr = generatePrimarySearchString({ ...extractedData, original_term: extractedData.original_term });
  const searchStr2 = generateSecondarySearchString(extractedData);
  const searchStr3 = generateTertiarySearchString(extractedData);

  return {
    finalStr: `${authors} ${article_title} ${journal_title} ${
      year ? year + ";" : ""
    }${volume ? volume : ""}${pages ? ":" + pages : ""}`,
    extractedData,
    searchStr,
    searchStr2,
    searchStr3,
  };
};

// Generate primary search string for PubMed - Search entire string
function generatePrimarySearchString(data) {
  if (!data) return "";

  // Strategy 1: Search the entire original string (most accurate)
  const originalTerm = data.original_term || data.term || "";
  if (originalTerm) {
    // Clean the original term for search
    return originalTerm
      .replace(/^\d+\.\s*/, "") // Remove numbering
      .replace(/\s*https?:\/\/(?:dx\.)?doi\.org\/\S+$/i, "") // Remove DOI URLs
      .trim();
  }

  return "";
}

// Generate secondary search string - Title + Journal + Authors
function generateSecondarySearchString(data) {
  if (!data) return "";

  const parts = [];

  // Strategy 2: Title + Journal + First Author
  if (data.article_title) {
    // Use significant words from title
    const titleWords = data.article_title
      .replace(/[^\w\s]/g, ' ')
      .split(' ')
      .filter(word => word.length > 3)
      .slice(0, 4);
    if (titleWords.length > 0) {
      parts.push(titleWords.join(' '));
    }
  }

  if (data.journal_title) {
    parts.push(`"${data.journal_title}"`);
  }

  if (data.authors) {
    const firstAuthor = data.authors.split(',')[0]?.trim();
    if (firstAuthor) {
      // Extract last name only
      const lastName = firstAuthor.split(' ')[0];
      if (lastName) {
        parts.push(lastName);
      }
    }
  }

  return parts.join(' AND ');
}

// Generate tertiary search string - Title + Journal only
function generateTertiarySearchString(data) {
  if (!data) return "";

  const parts = [];

  // Strategy 3: Just title + journal (most relaxed)
  if (data.article_title) {
    const titleWords = data.article_title
      .replace(/[^\w\s]/g, ' ')
      .split(' ')
      .filter(word => word.length > 4)
      .slice(0, 3);
    if (titleWords.length > 0) {
      parts.push(titleWords.join(' '));
    }
  }

  if (data.journal_title) {
    parts.push(`"${data.journal_title}"`);
  }

  return parts.join(' AND ');
}

export async function getJournalNameWithFallback(searchTerm) {
  const journal = await getJournalAbbreviation(searchTerm);
  if (journal) {
    return journal;
  }
  const journalWithAllFields = await getJournalAbbreviation(searchTerm, true);
  return journalWithAllFields || searchTerm;
}

const API_KEY = "4c5739b0da6cefd7734590674846548a8e08";

export async function getJournalAbbreviation(searchTerm, allFeilds = false) {
  const params = !allFeilds ? `[Journal]AND%22english%22[Language]` : ``;
  const searchUrl = `${nlmCatalog}${encodeURIComponent(
    searchTerm
  )}${params}&api_key=${API_KEY}&retmode=json&retmax=10`;

  try {
    const searchResponse = await delayedFetch(searchUrl);
    const searchData = await searchResponse.json();

    if (!searchData.esearchresult.idlist.length) {
      return null;
    }

    for await (const id of searchData.esearchresult.idlist) {
      const summaryUrl = `${nlmCatalogSummary}${id}&api_key=${API_KEY}&retmode=json`;
      const summaryResponse = await delayedFetch(summaryUrl);
      const summaryData = await summaryResponse.json();
      if (allFeilds && summaryData.result[id]) {
        return summaryData.result[id].medlineta;
      }
      if (
        !allFeilds &&
        summaryData.result[id] &&
        (summaryData.result[id].language === "eng" ||
          summaryData.result[id].language === "eng eng")
      ) {
        return summaryData.result[id].medlineta;
      }
    }
  } catch (error) {
    console.error("Error:", error);
    return null;
  }
}
