import { extract } from "../../constants/urls";
import {
  formatText,
  abbreviatePageNumbers,
  toSentenceCase,
  cleanTextForSearch,
} from "../../services/styling";
export async function extractDetailsFromAPI(content, Prompt) {

  try {
    // Fetch data from the API
    const response = await fetch(extract,{
      method: "POST",
      headers: {
        'Access-Control-Allow-Origin': '*',
        "Content-Type": 'application/json'
      },
      body: JSON.stringify({content: `${Prompt(content)}`})
    });
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    // Parse the JSON response
    const data = await response.json();
    // Extract citation details
    return data;
  } catch (error) {
    console.error("Error fetching CrossRef data:", error);
    return null;
  }
}

export const extractFormattedData = async (extractedData, skipJournalLookup = false) => {
  // Preserve original title for search, apply sentence case for display
  const original_article_title = extractedData?.article_title;
  const article_title = formatText(toSentenceCase(extractedData?.article_title));
  const authors = extractedData?.authors;
  const year = extractedData?.year;

  // Skip journal abbreviation lookup for PubMed (it provides journal data)
  // Only do lookup for CrossRef searches
  const journal_title = skipJournalLookup
    ? extractedData?.journal_title
    : await getJournalNameWithFallback(extractedData?.journal_title);

  const pages = abbreviatePageNumbers(extractedData?.pages);
  const volume = extractedData?.volume;

  // Create data object with original title preserved for search
  const dataForSearch = {
    ...extractedData,
    original_article_title,
    original_term: extractedData.original_term
  };

  // Generate search strings for PubMed (3 strategies)
  const searchStr = generatePrimarySearchString(dataForSearch);
  const searchStr2 = generateSecondarySearchString(dataForSearch);
  const searchStr3 = generateTertiarySearchString(dataForSearch);

  return {
    finalStr: `${authors} ${article_title} ${journal_title} ${
      year ? year + ";" : ""
    }${volume ? volume : ""}${pages ? ":" + pages : ""}`,
    extractedData,
    searchStr,
    searchStr2,
    searchStr3,
  };
};

// Generate primary search string for PubMed - Use structured extracted data
function generatePrimarySearchString(data) {
  if (!data) return "";

  const parts = [];

  // Strategy 1: Use extracted structured data for most accurate search
  if (data.article_title) {
    // Use original title for search (before sentence case formatting) and clean it
    const originalTitle = data.original_article_title || data.article_title;
    const cleanedTitle = cleanTextForSearch(originalTitle);
    parts.push(`"${cleanedTitle}[Title]`);
  }

  if (data.journal_title) {
    parts.push(`"${data.journal_title}[Journal]`);
  }

  if (data.authors) {
    // Use all authors for comprehensive search
    const authors = data.authors.split(',').map(author => author.trim()).filter(author => author);
    if (authors.length > 0) {
      // For primary search, use all authors with OR logic
      const authorSearchTerms = authors.map(author => `"${author}[Author]`);
      if (authorSearchTerms.length === 1) {
        parts.push(authorSearchTerms[0]);
      } else {
        parts.push(`(${authorSearchTerms.join(' OR ')})`);
      }
    }
  }



  return parts.join('+');
}

// Generate secondary search string - Title + Journal + Authors with PubMed tags
function generateSecondarySearchString(data) {
  if (!data) return "";

  const parts = [];

  // Strategy 2: Complete title + Journal + First Author
  if (data.article_title) {
    // Use original title for search (before sentence case formatting) and clean it
    const originalTitle = data.original_article_title || data.article_title;
    const cleanedTitle = cleanTextForSearch(originalTitle);
    parts.push(`"${cleanedTitle}[Title]`);
  }

  if (data.journal_title) {
    parts.push(`"${data.journal_title}[Journal]`);
  }

  if (data.authors) {
    // Use first author's full name for secondary search
    const firstAuthor = data.authors.split(',')[0]?.trim();
    if (firstAuthor) {
      parts.push(`"${firstAuthor}[Author]`);
    }
  }

  return parts.join('+');
}

// Generate tertiary search string - Title + Journal only with PubMed tags
function generateTertiarySearchString(data) {
  if (!data) return "";

  const parts = [];

  // Strategy 3: Complete title + journal (most relaxed)
  if (data.article_title) {
    // Use original title for search (before sentence case formatting) and clean it
    const originalTitle = data.original_article_title || data.article_title;
    const cleanedTitle = cleanTextForSearch(originalTitle);
    parts.push(`"${cleanedTitle}[Title]`);
  }

  if (data.journal_title) {
    parts.push(`"${data.journal_title}[Journal]`);
  }

  return parts.join('+');
}

export async function getJournalNameWithFallback(searchTerm) {
  const journal = await getJournalAbbreviation(searchTerm);
  if (journal) {
    return journal;
  }
  const journalWithAllFields = await getJournalAbbreviation(searchTerm, true);
  return journalWithAllFields || searchTerm;
}

// Runtime collection of journal mappings for this session
const sessionJournalMappings = new Map();

// Function to update journal mapping - logs new mappings for manual addition to journalMap.json
export function updateJournalMapping(originalJournal, abbreviatedJournal) {
  if (originalJournal && abbreviatedJournal) {
    // Store in session collection
    sessionJournalMappings.set(originalJournal, abbreviatedJournal);

    if (originalJournal !== abbreviatedJournal) {
      console.log(`📝 NEW JOURNAL MAPPING FOUND: "${originalJournal}" -> "${abbreviatedJournal}"`);
      console.log(`Add this to journalMap.json: "${originalJournal}": "${abbreviatedJournal}"`);
    } else {
      console.log(`✅ JOURNAL MAPPING CONFIRMED: "${originalJournal}" -> "${abbreviatedJournal}"`);
      console.log(`Consider adding to journalMap.json: "${originalJournal}": "${abbreviatedJournal}"`);
    }
  }
}

// Function to export all journal mappings found in this session
export function exportSessionJournalMappings() {
  const mappings = {};
  for (const [key, value] of sessionJournalMappings.entries()) {
    mappings[key] = value;
  }

  console.log("=== ALL JOURNAL MAPPINGS FROM THIS SESSION ===");
  console.log("Copy this to journalMap.json:");
  console.log(JSON.stringify(mappings, null, 2));

  return mappings;
}

// Function to clear session mappings
export function clearSessionJournalMappings() {
  sessionJournalMappings.clear();
  console.log("Session journal mappings cleared");
}

export async function getJournalAbbreviation(searchTerm) {
  // Use the existing journalMap.json and GenAI system
  const { searchJournalInGenAI } = await import("../../services/fetchFromGenAI");
  const journalFromMapOrGenAI = await searchJournalInGenAI(searchTerm);

  if (journalFromMapOrGenAI && journalFromMapOrGenAI !== searchTerm) {
    console.log(`Found journal mapping: "${searchTerm}" -> "${journalFromMapOrGenAI}"`);
    return journalFromMapOrGenAI;
  }
  // Fallback to original term if no mapping found
  console.log(`No journal mapping found for: "${searchTerm}", using original`);
  return searchTerm;
}
