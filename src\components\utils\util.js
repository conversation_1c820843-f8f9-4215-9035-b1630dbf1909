import { extract, nlmCatalog, nlmCatalogSummary } from "../../constants/urls";
import {
  delayedFetch,
  formatText,
  abbreviatePageNumbers,
  toSentenceCase,
} from "../../services/styling";
export async function extractDetailsFromAPI(content, Prompt) {

  try {
    // Fetch data from the API
    const response = await fetch(extract,{
      method: "POST",
      headers: {
        'Access-Control-Allow-Origin': '*',
        "Content-Type": 'application/json'
      },
      body: JSON.stringify({content: `${Prompt(content)}`})
    });
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    // Parse the JSON response
    const data = await response.json();
    // Extract citation details
    return data;
  } catch (error) {
    console.error("Error fetching CrossRef data:", error);
    return null;
  }
}

export const extractFormattedData = async (extractedData) => {
  // Preserve original title for search, apply sentence case for display
  const original_article_title = extractedData?.article_title;
  const article_title = formatText(toSentenceCase(extractedData?.article_title));
  const authors = extractedData?.authors;
  const year = extractedData?.year;
  const journal_title = await getJournalNameWithFallback(
    extractedData?.journal_title
  );
  const pages = abbreviatePageNumbers(extractedData?.pages);
  const volume = extractedData?.volume;

  // Create data object with original title preserved for search
  const dataForSearch = {
    ...extractedData,
    original_article_title,
    original_term: extractedData.original_term
  };

  // Generate search strings for PubMed (3 strategies)
  const searchStr = generatePrimarySearchString(dataForSearch);
  const searchStr2 = generateSecondarySearchString(dataForSearch);
  const searchStr3 = generateTertiarySearchString(dataForSearch);

  return {
    finalStr: `${authors} ${article_title} ${journal_title} ${
      year ? year + ";" : ""
    }${volume ? volume : ""}${pages ? ":" + pages : ""}`,
    extractedData,
    searchStr,
    searchStr2,
    searchStr3,
  };
};

// Generate primary search string for PubMed - Use structured extracted data
function generatePrimarySearchString(data) {
  if (!data) return "";

  const parts = [];

  // Strategy 1: Use extracted structured data for most accurate search
  if (data.article_title) {
    // Use original title for search (before sentence case formatting)
    const originalTitle = data.original_article_title || data.article_title;
    parts.push(`"${originalTitle}"[Title]`);
  }

  if (data.journal_title) {
    parts.push(`"${data.journal_title}"[Journal]`);
  }

  if (data.authors) {
    // Use all authors for comprehensive search
    const authors = data.authors.split(',').map(author => author.trim()).filter(author => author);
    if (authors.length > 0) {
      // For primary search, use all authors with OR logic
      const authorSearchTerms = authors.map(author => `"${author}"[Author]`);
      if (authorSearchTerms.length === 1) {
        parts.push(authorSearchTerms[0]);
      } else {
        parts.push(`(${authorSearchTerms.join(' OR ')})`);
      }
    }
  }

  if (data.year) {
    parts.push(`${data.year}[Publication Date]`);
  }

  return parts.join('+');
}

// Generate secondary search string - Title + Journal + Authors with PubMed tags
function generateSecondarySearchString(data) {
  if (!data) return "";

  const parts = [];

  // Strategy 2: Complete title + Journal + First Author
  if (data.article_title) {
    // Use original title for search (before sentence case formatting)
    const originalTitle = data.original_article_title || data.article_title;
    parts.push(`"${originalTitle}"[Title]`);
  }

  if (data.journal_title) {
    parts.push(`"${data.journal_title}"[Journal]`);
  }

  if (data.authors) {
    // Use first author's full name for secondary search
    const firstAuthor = data.authors.split(',')[0]?.trim();
    if (firstAuthor) {
      parts.push(`"${firstAuthor}"[Author]`);
    }
  }

  return parts.join('+');
}

// Generate tertiary search string - Title + Journal only with PubMed tags
function generateTertiarySearchString(data) {
  if (!data) return "";

  const parts = [];

  // Strategy 3: Complete title + journal (most relaxed)
  if (data.article_title) {
    // Use original title for search (before sentence case formatting)
    const originalTitle = data.original_article_title || data.article_title;
    parts.push(`"${originalTitle}"[Title]`);
  }

  if (data.journal_title) {
    parts.push(`"${data.journal_title}"[Journal]`);
  }

  return parts.join('+');
}

export async function getJournalNameWithFallback(searchTerm) {
  const journal = await getJournalAbbreviation(searchTerm);
  if (journal) {
    return journal;
  }
  const journalWithAllFields = await getJournalAbbreviation(searchTerm, true);
  return journalWithAllFields || searchTerm;
}

const API_KEY = "4c5739b0da6cefd7734590674846548a8e08";

export async function getJournalAbbreviation(searchTerm, allFeilds = false) {
  const params = !allFeilds ? `[Journal]AND%22english%22[Language]` : ``;
  const searchUrl = `${nlmCatalog}${encodeURIComponent(
    searchTerm
  )}${params}&api_key=${API_KEY}&retmode=json&retmax=10`;

  try {
    const searchResponse = await delayedFetch(searchUrl);
    const searchData = await searchResponse.json();

    if (!searchData.esearchresult.idlist.length) {
      return null;
    }

    for await (const id of searchData.esearchresult.idlist) {
      const summaryUrl = `${nlmCatalogSummary}${id}&api_key=${API_KEY}&retmode=json`;
      const summaryResponse = await delayedFetch(summaryUrl);
      const summaryData = await summaryResponse.json();
      if (allFeilds && summaryData.result[id]) {
        return summaryData.result[id].medlineta;
      }
      if (
        !allFeilds &&
        summaryData.result[id] &&
        (summaryData.result[id].language === "eng" ||
          summaryData.result[id].language === "eng eng")
      ) {
        return summaryData.result[id].medlineta;
      }
    }
  } catch (error) {
    console.error("Error:", error);
    return null;
  }
}
