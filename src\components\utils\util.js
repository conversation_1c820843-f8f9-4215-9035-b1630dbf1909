import { extract, nlmCatalog, nlmCatalogSummary } from "../../constants/urls";
import {
  delayedFetch,
  formatText,
  abbreviatePageNumbers,
} from "../../services/styling";
export async function extractDetailsFromAPI(content, Prompt) {

  try {
    // Fetch data from the API
    const response = await fetch(extract,{
      method: "POST",
      headers: {
        'Access-Control-Allow-Origin': '*',
        "Content-Type": 'application/json'
      },
      body: JSON.stringify({content: `${Prompt(content)}`})
    });
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    // Parse the JSON response
    const data = await response.json();
    // Extract citation details
    return data;
  } catch (error) {
    console.error("Error fetching CrossRef data:", error);
    return null;
  }
}

export const extractFormattedData = async (extractedData) => {
  const article_title = formatText(extractedData?.article_title);
  const authors = extractedData?.authors;
  const year = extractedData?.year;
  const journal_title = await getJournalNameWithFallback(
    extractedData?.journal_title
  );
  const pages = abbreviatePageNumbers(extractedData?.pages);
  const volume = extractedData?.volume;

  // Generate search strings for PubMed
  const searchStr = generatePrimarySearchString(extractedData);
  const searchStr2 = generateSecondarySearchString(extractedData);

  return {
    finalStr: `${authors} ${article_title} ${journal_title} ${
      year ? year + ";" : ""
    }${volume ? volume : ""}${pages ? ":" + pages : ""}`,
    extractedData,
    searchStr,
    searchStr2,
  };
};

// Generate primary search string for PubMed
function generatePrimarySearchString(data) {
  if (!data) return "";

  const parts = [];

  // Add authors (first author only for better matching)
  if (data.authors) {
    const firstAuthor = data.authors.split(',')[0]?.trim();
    if (firstAuthor) {
      parts.push(`${firstAuthor}[Author]`);
    }
  }

  // Add title keywords (first few significant words)
  if (data.article_title) {
    const titleWords = data.article_title
      .replace(/[^\w\s]/g, '')
      .split(' ')
      .filter(word => word.length > 3)
      .slice(0, 3);
    if (titleWords.length > 0) {
      parts.push(`"${titleWords.join(' ')}"`);
    }
  }

  // Add journal
  if (data.journal_title) {
    parts.push(`"${data.journal_title}"[Journal]`);
  }

  // Add year
  if (data.year) {
    parts.push(`${data.year}[Publication Date]`);
  }

  return parts.join(' AND ');
}

// Generate secondary search string (fallback strategy)
function generateSecondarySearchString(data) {
  if (!data) return "";

  const parts = [];

  // More relaxed search - just title and journal
  if (data.article_title) {
    const titleWords = data.article_title
      .replace(/[^\w\s]/g, '')
      .split(' ')
      .filter(word => word.length > 4)
      .slice(0, 5);
    if (titleWords.length > 0) {
      parts.push(titleWords.join(' '));
    }
  }

  if (data.journal_title) {
    parts.push(`"${data.journal_title}"`);
  }

  return parts.join(' AND ');
}

export async function getJournalNameWithFallback(searchTerm) {
  const journal = await getJournalAbbreviation(searchTerm);
  if (journal) {
    return journal;
  }
  const journalWithAllFields = await getJournalAbbreviation(searchTerm, true);
  return journalWithAllFields || searchTerm;
}

const API_KEY = "4c5739b0da6cefd7734590674846548a8e08";

export async function getJournalAbbreviation(searchTerm, allFeilds = false) {
  const params = !allFeilds ? `[Journal]AND%22english%22[Language]` : ``;
  const searchUrl = `${nlmCatalog}${encodeURIComponent(
    searchTerm
  )}${params}&api_key=${API_KEY}&retmode=json&retmax=10`;

  try {
    const searchResponse = await delayedFetch(searchUrl);
    const searchData = await searchResponse.json();

    if (!searchData.esearchresult.idlist.length) {
      return null;
    }

    for await (const id of searchData.esearchresult.idlist) {
      const summaryUrl = `${nlmCatalogSummary}${id}&api_key=${API_KEY}&retmode=json`;
      const summaryResponse = await delayedFetch(summaryUrl);
      const summaryData = await summaryResponse.json();
      if (allFeilds && summaryData.result[id]) {
        return summaryData.result[id].medlineta;
      }
      if (
        !allFeilds &&
        summaryData.result[id] &&
        (summaryData.result[id].language === "eng" ||
          summaryData.result[id].language === "eng eng")
      ) {
        return summaryData.result[id].medlineta;
      }
    }
  } catch (error) {
    console.error("Error:", error);
    return null;
  }
}
