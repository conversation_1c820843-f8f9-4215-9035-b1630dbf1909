import { extract } from "../../constants/urls";
import {
  formatText,
  abbreviatePageNumbers,
  toSentenceCase,
  cleanTextForSearch,
} from "../../services/styling";
import journalMap from "../../services/helpers/journalMap.json";

export async function extractDetailsFromAPI(content, Prompt) {

  try {
    // Fetch data from the API
    const response = await fetch(extract,{
      method: "POST",
      headers: {
        'Access-Control-Allow-Origin': '*',
        "Content-Type": 'application/json'
      },
      body: JSON.stringify({content: `${Prompt(content)}`})
    });
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    // Parse the JSON response
    const data = await response.json();
    // Extract citation details
    return data;
  } catch (error) {
    console.error("Error fetching CrossRef data:", error);
    return null;
  }
}

export const extractFormattedData = async (extractedData, skipJournalLookup = false) => {
  // Preserve original title for search, apply sentence case for display
  const original_article_title = extractedData?.article_title;
  const article_title = formatText(toSentenceCase(extractedData?.article_title));
  const authors = extractedData?.authors;
  const year = extractedData?.year;

  // Skip journal abbreviation lookup for PubMed (it provides journal data)
  // Only do lookup for CrossRef searches
  const journal_title = skipJournalLookup
    ? extractedData?.journal_title
    : await getJournalNameWithFallback(extractedData?.journal_title);

  const pages = abbreviatePageNumbers(extractedData?.pages);
  const volume = extractedData?.volume;

  // Create data object with original title preserved for search
  const dataForSearch = {
    ...extractedData,
    original_article_title,
    original_term: extractedData.original_term
  };

  // Generate search strings for PubMed (3 strategies)
  const searchStr = generatePrimarySearchString(dataForSearch);
  const searchStr2 = generateSecondarySearchString(dataForSearch);
  const searchStr3 = generateTertiarySearchString(dataForSearch);

  return {
    finalStr: `${authors} ${article_title} ${journal_title} ${
      year ? year + ";" : ""
    }${volume ? volume : ""}${pages ? ":" + pages : ""}`,
    extractedData,
    searchStr,
    searchStr2,
    searchStr3,
  };
};

// Generate primary search string for PubMed - Use structured extracted data
function generatePrimarySearchString(data) {
  if (!data) return "";

  const parts = [];

  // Strategy 1: Use extracted structured data for most accurate search
  if (data.article_title) {
    // Use original title for search (before sentence case formatting) and clean it
    const originalTitle = data.original_article_title || data.article_title;
    const cleanedTitle = cleanTextForSearch(originalTitle);
    parts.push(`"${cleanedTitle}[Title]`);
  }

  if (data.journal_title) {
    parts.push(`"${data.journal_title}[Journal]`);
  }

  if (data.authors) {
    // Use all authors for comprehensive search
    const authors = data.authors.split(',').map(author => author.trim()).filter(author => author);
    if (authors.length > 0) {
      // For primary search, use all authors with OR logic
      const authorSearchTerms = authors.map(author => `"${author}[Author]`);
      if (authorSearchTerms.length === 1) {
        parts.push(authorSearchTerms[0]);
      } else {
        parts.push(`(${authorSearchTerms.join(' OR ')})`);
      }
    }
  }



  return parts.join('+');
}

// Generate secondary search string - Title + Journal + Authors with PubMed tags
function generateSecondarySearchString(data) {
  if (!data) return "";

  const parts = [];

  // Strategy 2: Complete title + Journal + First Author
  if (data.article_title) {
    // Use original title for search (before sentence case formatting) and clean it
    const originalTitle = data.original_article_title || data.article_title;
    const cleanedTitle = cleanTextForSearch(originalTitle);
    parts.push(`"${cleanedTitle}[Title]`);
  }

  if (data.journal_title) {
    parts.push(`"${data.journal_title}[Journal]`);
  }

  if (data.authors) {
    // Use first author's full name for secondary search
    const firstAuthor = data.authors.split(',')[0]?.trim();
    if (firstAuthor) {
      parts.push(`"${firstAuthor}[Author]`);
    }
  }

  return parts.join('+');
}

// Generate tertiary search string - Title + Journal only with PubMed tags
function generateTertiarySearchString(data) {
  if (!data) return "";

  const parts = [];

  // Strategy 3: Complete title + journal (most relaxed)
  if (data.article_title) {
    // Use original title for search (before sentence case formatting) and clean it
    const originalTitle = data.original_article_title || data.article_title;
    const cleanedTitle = cleanTextForSearch(originalTitle);
    parts.push(`"${cleanedTitle}[Title]`);
  }

  if (data.journal_title) {
    parts.push(`"${data.journal_title}[Journal]`);
  }

  return parts.join('+');
}

export async function getJournalNameWithFallback(searchTerm) {
  const journal = await getJournalAbbreviation(searchTerm);
  if (journal) {
    return journal;
  }
  const journalWithAllFields = await getJournalAbbreviation(searchTerm, true);
  return journalWithAllFields || searchTerm;
}

// Function to update journal mapping - updates the existing journalMap.json
export async function updateJournalMapping(originalJournal, abbreviatedJournal) {
  if (originalJournal && abbreviatedJournal) {
    try {
      // Force fresh import by adding timestamp to bypass cache
      const timestamp = Date.now();
      const currentJournalMap = { ...journalMapModule.default }; // Create a copy

      console.log(`📚 Fresh journalMap loaded with ${Object.keys(currentJournalMap).length} entries`);
      console.log(`📋 Current keys:`, Object.keys(currentJournalMap).slice(0, 5), '...');

      // Check if mapping already exists
      const existingMapping = currentJournalMap[originalJournal];
      console.log(`🔍 Checking if "${originalJournal}" exists in journalMap:`, existingMapping);

      if (existingMapping) {
        console.log(`✅ Journal mapping already exists: "${originalJournal}" -> "${existingMapping}"`);
        return;
      }

      // Add new mapping to the copy
      currentJournalMap[originalJournal] = abbreviatedJournal;

      // Since we can't write to the file directly in browser, let's create a downloadable updated version
      const updatedMapString = JSON.stringify(currentJournalMap, null, 2);

      console.log(`📝 WOULD ADD to journalMap.json: "${originalJournal}" -> "${abbreviatedJournal}"`);
      console.log(`📥 Download updated journalMap.json with ${Object.keys(currentJournalMap).length} entries`);

      // Create downloadable file
      const blob = new Blob([updatedMapString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'journalMap_updated.json';
      a.style.display = 'none';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

    } catch (error) {
      console.error('Error updating journal mapping:', error);
      console.log(`Manual addition needed: "${originalJournal}": "${abbreviatedJournal}"`);
    }
  }
}

export async function getJournalAbbreviation(searchTerm) {
  // Use the existing journalMap.json and GenAI system
  const { searchJournalInGenAI } = await import("../../services/fetchFromGenAI");
  const journalFromMapOrGenAI = await searchJournalInGenAI(searchTerm);

  if (journalFromMapOrGenAI && journalFromMapOrGenAI !== searchTerm) {
    console.log(`Found journal mapping: "${searchTerm}" -> "${journalFromMapOrGenAI}"`);
    return journalFromMapOrGenAI;
  }
  // Fallback to original term if no mapping found
  console.log(`No journal mapping found for: "${searchTerm}", using original`);
  return searchTerm;
}
