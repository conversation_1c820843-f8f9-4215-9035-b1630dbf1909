{"name": "te-backend", "version": "1.0.0", "description": "TE Backend API for journal mapping updates", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["journal", "mapping", "api", "express"], "author": "TE Team", "license": "ISC", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}