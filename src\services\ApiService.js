/**
 * Unified API service for all external API calls
 */
import { delayedFetch } from '../utils/apiUtils';
import { formatCitation, titleSimilarityCheck } from '../utils/formatUtils';
import { 
  esearch, 
  esummary, 
  crossRef, 
  nlmCatalog, 
  nlmCatalogSummary 
} from '../constants/urls';
import { 
  apiKey, 
  db, 
  retmax, 
  retmode 
} from '../constants/env';

/**
 * Unified API service class
 */
export class ApiService {
  /**
   * Search PubMed for a term
   * @param {string} term - Search term
   * @returns {Promise<Object>} - PubMed search results
   */
  static async searchPubMed(term) {
    if (!term) return null;
    
    try {
      const encodedTerm = encodeURI(
        term.trim()
          .replaceAll("&amp;", "")
          .replaceAll("&", "")
          .replace(/\s+/g, "+")
      );
      
      const searchUrl = `${esearch}?db=${db}&term=${encodedTerm}&retmode=${retmode}&retmax=${retmax}&api_key=${apiKey}`;
      
      const searchResponse = await delayedFetch(searchUrl);
      if (!searchResponse.ok) throw new Error("Search API failed");
      
      const searchResult = await searchResponse.json();
      if (!searchResult.esearchresult.idlist.length) return null;
      
      const pmids = searchResult.esearchresult.idlist.join(",");
      const summaryUrl = `${esummary}?db=${db}&id=${pmids}&retmode=${retmode}&api_key=${apiKey}`;
      
      const summaryResponse = await delayedFetch(summaryUrl);
      if (!summaryResponse.ok) throw new Error("Summary API failed");
      
      return await summaryResponse.json();
    } catch (error) {
      console.error("Error in PubMed search:", error);
      return null;
    }
  }
  
  /**
   * Search CrossRef for a term
   * @param {string} searchTerm - Search term
   * @param {Object} ref - Reference object
   * @returns {Promise<Object>} - CrossRef search results
   */
  static async searchCrossRef(searchTerm, ref) {
    if (!searchTerm) return null;
    
    try {
      const baseUrl = `${crossRef}?query=`;
      const encodedSearchTerm = encodeURIComponent(searchTerm);
      const fullUrl = `${baseUrl}${encodedSearchTerm}&rows=1`;
      
      const response = await delayedFetch(fullUrl);
      if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
      
      const data = await response.json();
      if (!data.message.items.length) return null;
      
      return data;
    } catch (error) {
      console.error("Error in CrossRef search:", error);
      return null;
    }
  }
  
  /**
   * Get journal information
   * @param {string} searchTerm - Journal name
   * @param {boolean} allFields - Whether to include all fields
   * @returns {Promise<Object>} - Journal information
   */
  static async getJournalInfo(searchTerm, allFields = false) {
    if (!searchTerm) return null;
    
    try {
      const params = !allFields ? `[Journal]AND%22english%22[Language]` : ``;
      const searchUrl = `${nlmCatalog}${encodeURIComponent(
        searchTerm.replaceAll("&", " and ")
      )}${params}&api_key=${apiKey}&retmode=json&retmax=1`;
      
      const searchResponse = await delayedFetch(searchUrl);
      if (!searchResponse.ok) throw new Error("Journal search failed");
      
      const searchData = await searchResponse.json();
      if (!searchData.esearchresult.idlist.length) return null;
      
      const id = searchData.esearchresult.idlist[0];
      const summaryUrl = `${nlmCatalogSummary}${id}&api_key=${apiKey}&retmode=json`;
      
      const summaryResponse = await delayedFetch(summaryUrl);
      if (!summaryResponse.ok) throw new Error("Journal summary failed");
      
      const summaryData = await summaryResponse.json();
      return summaryData.result[id];
    } catch (error) {
      console.error("Error in journal info:", error);
      return null;
    }
  }
  
  /**
   * Process PubMed search results
   * @param {Array} references - References to search
   * @returns {Promise<Object>} - Processed search results
   */
  static async processPubMedSearch(references) {
    if (!references || !Array.isArray(references)) {
      return { pubMedArray: [], notFoundArray: [], multipleFound: [] };
    }
    
    const pubMedArray = [];
    const notFoundArray = [];
    const multipleFound = [];
    
    for (const ref of references) {
      try {
        // First attempt with primary search string
        const searchResult = await this.searchPubMed(ref.searchStr);
        let found = searchResult;
        let extractedData = formatCitation(
          searchResult?.result?.[searchResult?.result?.uids?.[0]]
        );
        let isMultipleArticles = searchResult?.result?.uids?.length > 1;
        
        // Try secondary search if first one failed
        if ((!found || !extractedData) && ref.searchStr2) {
          const secondSearchResult = await this.searchPubMed(ref.searchStr2);
          found = secondSearchResult;
          extractedData = formatCitation(
            secondSearchResult?.result?.[secondSearchResult?.result?.uids?.[0]]
          );
          isMultipleArticles = secondSearchResult?.result?.uids?.length > 1;
        }
        
        if (found && extractedData) {
          pubMedArray.push({ ...ref, ...extractedData, type: "FOUND", found });
        } else if (isMultipleArticles) {
          multipleFound.push({
            ...ref,
            ...extractedData,
            type: "MULTIPLE_PubMed",
            found,
          });
        } else {
          notFoundArray.push(ref);
        }
      } catch (error) {
        console.error("Error in PubMed processing:", error);
        notFoundArray.push(ref);
      }
    }
    
    return { pubMedArray, notFoundArray, multipleFound };
  }
}
