import { useState, useEffect, useCallback } from "react";
import { filterReferencesByURL } from "../services/filterReferencesByURL";
import { ApiService } from "../services/ApiService";
import {
  createReferenceObjects,
  sortMultipleArrays,
  createInitialState,
  createFinalState,
  updateProgress
} from "../utils/referenceUtils";

const useFetchPubMedData = (searchTerms, usePubMed = true, setProg) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [progress, setProgress] = useState(0);

  const fetchPubMedData = useCallback(
    async (terms) => {
      try {
        setLoading(true);
        setError(null);
        const extractedRefs = createReferenceObjects(terms);
        setProgress(0); //
        const totalSteps = extractedRefs.length;
        let completedSteps = 0;

        const duplicateRefArrays = [];
        const nonDuplicateRefs = [];

        const { urlArray, noURLArray } = filterReferencesByURL(extractedRefs);
        completedSteps += urlArray.length;
        setProgress(updateProgress(completedSteps, totalSteps));

        if (usePubMed) {
          // Use the unified ApiService for PubMed search
          const { pubMedArray, notFoundArray, multipleFound } = await ApiService.processPubMedSearch(
            noURLArray
          );

          completedSteps += pubMedArray.length;
          setProgress(updateProgress(completedSteps, totalSteps));

          if (notFoundArray.length === 0) {
            setData(() =>
              createInitialState(urlArray, pubMedArray, notFoundArray)
            );
            return;
          }

          // Process remaining references with CrossRef
          const crossRefResults = await Promise.all(
            notFoundArray.map(ref => ApiService.searchCrossRef(ref.term, ref))
          );

          const crossRef = crossRefResults
            .filter(Boolean)
            .map((result, index) => ({
              ...notFoundArray[index],
              type: "FOUND",
              source: result.message?.items?.[0]?.["container-title"]?.[0] || ""
            }));

          const notFoundSCArray = notFoundArray.filter((_, index) => !crossRefResults[index]);

          completedSteps += crossRef.length + notFoundSCArray.length;
          setProgress(updateProgress(completedSteps, totalSteps));

          const sortedArray = sortMultipleArrays(
            pubMedArray,
            crossRef,
            urlArray,
            notFoundSCArray
          );

          setData(() => createFinalState(sortedArray));
        } else {
          // Direct CrossRef search without PubMed
          const crossRefResults = await Promise.all(
            noURLArray.map(ref => ApiService.searchCrossRef(ref.term, ref))
          );

          const crossRef = crossRefResults
            .filter(Boolean)
            .map((result, index) => ({
              ...noURLArray[index],
              type: "FOUND",
              source: result.message?.items?.[0]?.["container-title"]?.[0] || ""
            }));

          const notFoundSCArray = noURLArray.filter((_, index) => !crossRefResults[index]);

          const sortedArray = sortMultipleArrays(
            crossRef,
            notFoundSCArray,
            urlArray,
            duplicateRefArrays
          );

          setData(() => createFinalState(sortedArray));
        }
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    },
    [usePubMed]
  );

  useEffect(() => {
    if (searchTerms?.length > 0) {
      fetchPubMedData(searchTerms);
    }
  }, [searchTerms, fetchPubMedData]);

  useEffect(() => {
    console.log("progress", progress);
    setProg(progress);
  }, [progress]);

  return { data, loading, error, setData, setLoading };
};

export default useFetchPubMedData;
