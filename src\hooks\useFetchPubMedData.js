import { useState, useEffect, useCallback } from "react";
import { filterReferencesByURL } from "../services/filterReferencesByURL";
import { searchInPubMed } from "../services/searchInPubMed";
import { searchInCrossRef } from "../services/searchInCrossRef";
import {
  createReferenceObjects,
  sortMultipleArrays,
} from "../services/helpers/arrayHelpers";
import {
  createFinalState,
  updateProgress,
} from "../services/helpers/stateHelpers";

const useFetchPubMedData = (searchTerms, usePubMed = true, setProg) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [progress, setProgress] = useState(0);

  const fetchPubMedData = useCallback(
    async (terms) => {
      try {
        setLoading(true);
        setError(null);
        const extractedRefs = createReferenceObjects(terms);
        setProgress(0);
        // Calculate total steps properly: URL refs are instant, others need processing
        const totalSteps = extractedRefs.length;
        let completedSteps = 0;

        const duplicateRefArrays = [];

        const { urlArray, noURLArray } = filterReferencesByURL(extractedRefs);
        // URL references are processed instantly
        completedSteps += urlArray.length;
        setProgress(updateProgress(completedSteps, totalSteps));

        if (usePubMed) {
          // Step 1: Search in PubMed
          const { pubMedArray, notFoundArray } = await searchInPubMed(
            noURLArray
          );
          // Update progress after PubMed search
          completedSteps += pubMedArray.length;
          setProgress(updateProgress(completedSteps, totalSteps));

          // If all references found in PubMed, we're done
          if (notFoundArray.length === 0) {
            setProgress(100);
            setData(() =>
              createFinalState(sortMultipleArrays(pubMedArray, [], urlArray, []))
            );
            return;
          }

          // Step 2: Search remaining references in CrossRef
          const { crossRef, notFoundSCArray } = await searchInCrossRef(
            notFoundArray
          );
          // Update progress after CrossRef search
          completedSteps += crossRef.length + notFoundSCArray.length;
          setProgress(100); // All processing complete

          console.log(crossRef, notFoundSCArray, "crossref final");
          const sortedArray = sortMultipleArrays(
            pubMedArray,
            crossRef,
            urlArray,
            notFoundSCArray
          );
          setData(() => createFinalState(sortedArray));
          console.log(
            pubMedArray,
            crossRef,
            urlArray,
            notFoundSCArray,
            "Final"
          );
        } else {
          // Direct CrossRef search (no PubMed)
          const { crossRef, notFoundSCArray } = await searchInCrossRef(
            noURLArray
          );
          completedSteps += crossRef.length + notFoundSCArray.length;
          setProgress(100); // All processing complete

          const sortedArray = sortMultipleArrays(
            crossRef,
            notFoundSCArray,
            urlArray,
            duplicateRefArrays
          );
          setData(() => createFinalState(sortedArray));
        }
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    },
    [usePubMed]
  );

  useEffect(() => {
    if (searchTerms?.length > 0) {
      fetchPubMedData(searchTerms);
    }
  }, [searchTerms, fetchPubMedData]);

  useEffect(() => {
    console.log("progress", progress);
    if (setProg) {
      setProg(progress);
    }
  }, [progress, setProg]);

  return { data, loading, error, setData, setLoading };
};

export default useFetchPubMedData;
