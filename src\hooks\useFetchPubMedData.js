import { useState, useEffect, useCallback } from "react";
import { filterReferencesByURL } from "../services/filterReferencesByURL";
import { searchInPubMed } from "../services/searchInPubMed";
import { searchInCrossRef } from "../services/searchInCrossRef";
import {
  createReferenceObjects,
  sortMultipleArrays,
} from "../services/helpers/arrayHelpers";
import {
  createInitialState,
  createFinalState,
  updateProgress,
} from "../services/helpers/stateHelpers";

const useFetchPubMedData = (searchTerms, usePubMed = true, setProg) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [progress, setProgress] = useState(0);

  const fetchPubMedData = useCallback(
    async (terms) => {
      try {
        setLoading(true);
        setError(null);
        const extractedRefs = createReferenceObjects(terms);
        setProgress(0); //
        const totalSteps = extractedRefs.length;
        let completedSteps = 0;

        const duplicateRefArrays = [];

        const { urlArray, noURLArray } = filterReferencesByURL(extractedRefs);
        completedSteps += urlArray.length;
        setProgress(updateProgress(completedSteps, totalSteps));

        if (usePubMed) {
          const { pubMedArray, notFoundArray } = await searchInPubMed(
            noURLArray
          );
          completedSteps += pubMedArray.length;
          setProgress(updateProgress(completedSteps, totalSteps));
          if (notFoundArray.length === 0) {
            setData(() =>
              createInitialState(urlArray, pubMedArray, notFoundArray)
            );
            return;
          }
          const { crossRef, notFoundSCArray } = await searchInCrossRef(
            notFoundArray
          );
          completedSteps += crossRef.length + notFoundSCArray.length;
          setProgress(updateProgress(completedSteps, totalSteps));
          console.log(crossRef, notFoundSCArray, "crossref final");
          const sortedArray = sortMultipleArrays(
            pubMedArray,
            crossRef,
            urlArray,
            notFoundSCArray
          );
          setData(() => createFinalState(sortedArray));
          console.log(
            pubMedArray,
            crossRef,
            urlArray,
            notFoundSCArray,
            "Final"
          );

          // completedSteps += notFoundSCArray.length;
          // setProgress(updateProgress(completedSteps, totalSteps));
        } else {
          const { crossRef, notFoundSCArray } = await searchInCrossRef(
            noURLArray
          );
          const sortedArray = sortMultipleArrays(
            crossRef,
            notFoundSCArray,
            urlArray,
            duplicateRefArrays
          );
          setData(() => createFinalState(sortedArray));
        }
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    },
    [usePubMed]
  );

  useEffect(() => {
    if (searchTerms?.length > 0) {
      fetchPubMedData(searchTerms);
    }
  }, [searchTerms, fetchPubMedData]);

  useEffect(() => {
    console.log("progress", progress);
    if (setProg) {
      setProg(progress);
    }
  }, [progress, setProg]);

  return { data, loading, error, setData, setLoading };
};

export default useFetchPubMedData;
