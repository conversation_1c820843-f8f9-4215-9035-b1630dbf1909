import logging
from flask import Flask, request, jsonify
from flask_cors import CORS
from werkzeug.utils import secure_filename
import docx
import unicodedata
import os
import time
import re
import requests
import json
from dotenv import load_dotenv  # Load environment variables from .env

load_dotenv()

# Initialize Flask app and CORS
app = Flask(__name__)
CORS(app)

UPLOAD_FOLDER = 'uploads'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Configure Logging
logging.basicConfig(
    filename='app.log',
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)


@app.route('/test', methods=['GET'])
def test_get():
    return jsonify({"message": "Test route working"}), 200


@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return jsonify({"error": "No file part in the request"}), 400

    file = request.files['file']

    if file.filename == '':
        return jsonify({"error": "No file selected for uploading"}), 400

    if file and file.filename.endswith('.docx'):
        filename = secure_filename(file.filename)
        timestamp = int(time.time())  # Avoid overwriting
        filename = f"{timestamp}_{filename}"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)

        try:
            file.save(filepath)
            references = extract_references(filepath)

            if not references:
                return jsonify({"error": "No references found"}), 400

            return jsonify({"references": references})

        except Exception as e:
            logging.error(f"Error processing file {filename}: {e}")
            return jsonify({"error": "Internal server error"}), 500

    return jsonify({"error": "Only .docx files are allowed"}), 400

def extract_references(filepath):
    try:
        doc = docx.Document(filepath)
        references = []
        is_reference_section = False

        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()

            if not is_reference_section:
            # Detect the start of the References section (handling variations)
                if "references" in text.lower() or "bibliography" in text.lower() or "references:" in text.lower():
                    print("References Section:", text)  # Debug print
                    is_reference_section = True
                    continue

            # Stop processing if another major section starts
            if is_reference_section:
                if any(text.lower().startswith(kw) for kw in ["figure", "tables", "acknowledgement"]):
                    print("Stopping at section:", text)
                    break
            # Add valid reference lines, cleaned of Unicode
                if text:
                    print("Valid reference:", text)  # Debug print
                    cleaned_text = unicodedata.normalize("NFKD", text)
                    references.append(cleaned_text)

        return references if references else ["No references found"]
    except Exception as e:
        logging.error(f"Error extracting references: {e}")
        return []


API_KEY = os.getenv("OPENAI_API_KEY")
OPENAI_API_URL = "https://api.openai.com/v1/chat/completions"

def extract_details(content):
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {API_KEY}",
    }
    
    payload = {
        "model": "gpt-4o-mini",
        "messages": [
            {"role": "system", "content": "You are an assistant that extracts structured data from strings."},
            {"role": "user", "content": content},
        ],
        "max_tokens": 500,
    }
    
    try:
        response = requests.post(OPENAI_API_URL, headers=headers, json=payload)
        result = response.json()

        if response.status_code == 200 and "choices" in result and result["choices"]:
            content = result["choices"][0]["message"]["content"].strip()
            content = content.replace("```json", "").replace("```", "").strip()  # Clean extraneous markdown
            return json.loads(content)  # Convert string to JSON
        else:
            return {"error": "Failed to extract details", "response": result}
    
    except Exception as e:
        return {"error": "Error interacting with OpenAI API", "details": str(e)}

@app.route("/extract", methods=["POST"])
def extract():
    data = request.json
    
    extracted_data = extract_details(data["content"])  # Convert string function to callable
    return jsonify(extracted_data)

if __name__ == "__main__":
    app.run(debug=True, port=4999)
