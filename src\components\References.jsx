import React, { useState } from "react";
import axios from "axios";
import { upload } from '../constants/urls';
import ErrorNotification from "./features/ErrorNotification";
const References = ({ onSearch }) => {
  const [file, setFile] = useState(null);
  const [error, setError] = useState('');

  const handleFileChange = (e) => {
    setFile(e.target.files[0]);
    setError('');
  };

  const handleUpload = async () => {
    if (!file) {
      setError("Please select a file first!");
      return;
    }

    const formData = new FormData();
    formData.append("file", file);

    onSearch({ isLoading: true, data: [], error: null });

    try {
      const response = await axios.post(upload, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
          'Access-Control-Allow-Origin': '*'
        },
      });
      
      onSearch({
        isLoading: false,
        data: response.data.references,
        error: null
      });
    } catch (error) {
      console.error("Error uploading file:", error);
      onSearch({
        isLoading: false,
        data: [],
        error: "Failed to upload file. Please try again."
      });
      setError("Failed to upload file. Please try again.");
    }
  };

  return (
    <div className="references-container">
      <h1>Upload .docx File</h1>
      <div className="upload-controls">
        <input 
          type="file" 
          onChange={handleFileChange} 
          accept=".docx"
          className="file-input" 
        />
        <button 
          onClick={handleUpload}
          className="upload-button"
        >
          Upload
        </button>
      </div>
      {error && <ErrorNotification message={error} />}
    </div>
  );
};

export default References;
