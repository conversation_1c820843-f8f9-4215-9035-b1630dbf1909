/**
 * Centralized formatting utilities for text processing
 */

/**
 * Formats text by ensuring it ends with a period
 * @param {string} text - The text to format
 * @returns {string} - Formatted text with trailing period
 */
export const formatText = (text) => {
  if (!text) return '';
  return !customEndsWith(text, '.') ? `${text}.` : text;
};

/**
 * Capitalizes the first letter after a colon in text
 * @param {string} text - The text to format
 * @returns {string} - Text with capitalized first letter after colon
 */
export const capitalizeAfterColon = (text) => {
  if (!text) return '';
  
  return text.replace(/:\s*([a-z])/, (match, p1) => {
    return ': ' + p1.toUpperCase();
  });
};

/**
 * Checks if a string ends with a specific substring
 * @param {string} str - The string to check
 * @param {string} searchString - The substring to look for at the end
 * @returns {boolean} - True if the string ends with the substring
 */
export function customEndsWith(str, searchString) {
  if (!str || str.length < searchString.length) return false;
  return str.slice(-searchString.length) === searchString;
}

/**
 * Abbreviates page number ranges (e.g., "123-128" becomes "123-8")
 * @param {string} pages - The page range to abbreviate
 * @returns {string} - Abbreviated page range
 */
export function abbreviatePageNumbers(pages) {
  if (!pages) return pages;
  
  // Handle prefixed page numbers (e.g., S613-S618)
  const prefixMatch = pages.match(/^([A-Za-z]*)(\d+)-([A-Za-z]*)(\d+)/);
  if (prefixMatch) {
    const [_, prefix1, start, prefix2, end] = prefixMatch;
    // Convert to numbers for comparison
    const startNum = parseInt(start);
    const endNum = parseInt(end);
    
    // If start and end are the same number, return just one number
    if (startNum === endNum) {
      return prefix1 + start;
    }
    
    const commonPrefix = compareStringsAndStop(start, end);
    return `${prefix1}${start}-${commonPrefix}`;
  }
  
  // Handle regular page numbers (e.g., 123-128)
  const regularMatch = pages.match(/^(\d+)-(\d+)/);
  if (regularMatch) {
    const [start, end] = pages.split("-").map(p => p.trim());
    const startNum = parseInt(start);
    const endNum = parseInt(end);
    
    if (startNum === endNum) {
      return start;
    }
    
    const commonPrefix = compareStringsAndStop(start, end);
    return `${start}-${commonPrefix}`;
  }
  
  return pages;
}

/**
 * Helper function for abbreviatePageNumbers
 * Compares two strings and returns the differing part of the second string
 */
function compareStringsAndStop(s1, s2) {
  // If second number is shorter, return full second number
  if (s2.length < s1.length) {
    return s2;
  }
  
  // Find where numbers start to differ
  let i = 0;
  while (i < s1.length && s1[i] === s2[i]) {
    i++;
  }
  
  // Return the differing part
  return s2.slice(i);
}

/**
 * Removes issue numbers from volume-issue-pages format
 * @param {string} volumeIssuePages - The volume-issue-pages string
 * @returns {string} - String with issue numbers removed
 */
export const removeIssueNumbers = (volumeIssuePages) => {
  if (!volumeIssuePages) return '';
  return volumeIssuePages.replace(/\((\d+)\)/, "");
};

/**
 * Validates and formats author names according to citation standards
 * @param {string} authors - The author names string
 * @returns {string} - Formatted author names
 */
export const validateAuthorNames = (authors) => {
  if (!authors) return '';
  
  const authorList = authors.split(",").map((name) => name.trim());
  if (authorList.length > 6) {
    return `${authorList.slice(0, 6).join(", ")}, et al.`;
  }
  return authors;
};

/**
 * Formats a complete citation from article data
 * @param {Object} article - The article data
 * @returns {Object|null} - Formatted citation object
 */
export function formatCitation(article) {
  if (!article) return null;

  const formatAuthors = (authors) => {
    const authorList = authors?.map(author => author.name);
    const formattedAuthors = authorList.length > 6 
      ? `${authorList.slice(0, 6).join(', ')}, et al.`
      : authorList.join(", ");
    return formatText(formattedAuthors);
  };

  const authors = formatAuthors(article.authors);
  const title = capitalizeAfterColon(formatText(article.title));
  const year = article.sortpubdate?.split("/")[0];
  const page = abbreviatePageNumbers(article?.pages || '');

  return {
    finalStr: `${authors} ${title} ${article.source} ${year ? year + ';' : ''}${article.volume}${page ? ':' + page : ''}.`
  };
}

/**
 * Checks similarity between two titles
 * @param {string} title1 - First title
 * @param {string} title2 - Second title
 * @returns {boolean} - True if titles are similar
 */
export function titleSimilarityCheck(title1, title2) {
  if (!title1 || !title2) return false;

  const normalize = (str) => {
    const stopwords = new Set([
      "the", "and", "is", "a", "an", "on", "in", "at"
    ]);
    return str
      .toLowerCase()
      .normalize("NFC") // Normalize Unicode
      .replace(/[^\w\s]/g, "")
      .split(/\s+/)
      .filter((word) => word.length > 2 && !stopwords.has(word));
  };

  const words1 = new Set(normalize(title1));
  const words2 = new Set(normalize(title2));

  const intersectionSize = [...words1].filter((word) =>
    words2.has(word)
  ).length;
  const avgSize = (words1.size + words2.size) / 2;

  const similarity = intersectionSize / avgSize;

  return similarity >= 0.6; // 60% threshold
}
