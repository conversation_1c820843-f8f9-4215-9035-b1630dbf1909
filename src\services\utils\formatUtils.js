
export const formatText = (text) => !customEndsWith(text, '.') ? `${text}.` : text;

export const capitalizeAfterColon = (text) => {
  if (!text) return '';

  return text.replace(/:\s*([a-z])/, (match, p1) => {
    return ': ' + p1.toUpperCase();
  });
}
export function formatCitation(article) {
  if (!article) return null;

  const formatAuthors = (authors) => {
    const authorList = authors?.map(author => author.name);
    const formattedAuthors = authorList.length > 6 
      ? `${authorList.slice(0, 6).join(', ')}, et al.`
      : authorList.join(", ");
    return formatText(formattedAuthors);
  };

  const authors = formatAuthors(article.authors);
  const title = capitalizeAfterColon(formatText(article.title));
  const year = article.sortpubdate?.split("/")[0];
  const page = abbreviatePageNumbers(article?.pages || '');

  return {
    finalStr: `${authors} ${title} ${article.source} ${year ? year + ';' : ''}${article.volume}${page ? ':' + page : ''}.`
  };
}

export function customEndsWith(str, searchString) {
  if (str?.length < searchString.length) return false;
  return str?.slice(-searchString.length) === searchString;
}

export function abbreviatePageNumbers(pages) {

  const match = pages?.match(/^(\d+)-(\d+)/);
  if (match) {
    const [start, end] = pages?.split('-');
    const commonPrefix = compareStringsAndStop(start, end);
    return `${start}-${commonPrefix}`;
  }
  return pages;
}

function compareStringsAndStop(s1, s2) {
  let result = "";
  let minLength = Math.min(s1.length, s2.length);
  
  for (let i = 0; i < minLength; i++) {
    if (s1[i] === s2[i]) continue;
    result = s2.slice(i);
    break;
  }
  
  return result || s2;
}