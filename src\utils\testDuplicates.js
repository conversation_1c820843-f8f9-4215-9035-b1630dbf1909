/**
 * Test utility to verify duplicate reference checking functionality
 */
import { createReferenceObjects } from '../services/helpers/arrayHelpers';

// Test data with known duplicates
const testReferences = [
  "1. <PERSON>, <PERSON><PERSON>. A study on cancer research. Nature. 2023;123:45-50.",
  "2. <PERSON>, <PERSON>. Machine learning applications. Science. 2022;456:78-90.",
  "3. <PERSON>, <PERSON><PERSON> study on cancer research. Nature. 2023;123:45-8.", // Duplicate with different page format
  "4. <PERSON>, <PERSON>. Climate change effects. Cell. 2021;789:12-25.",
  "5. <PERSON>, <PERSON><PERSON>. A study on cancer research. Nature. 2023;123(4):45-50.", // Duplicate with issue number
  "6. <PERSON>, Wilson C. Machine learning applications. Science. 2022;456:78-90.", // Exact duplicate
];

/**
 * Test the duplicate checking functionality
 */
export const testDuplicateChecking = () => {
  console.log("Testing duplicate reference checking...");
  
  const processedRefs = createReferenceObjects(testReferences);
  
  console.log("Original references:", testReferences.length);
  console.log("Processed references:", processedRefs.length);
  
  const duplicates = processedRefs.filter(ref => ref.MarkType === "DUPLICATE");
  console.log("Found duplicates:", duplicates.length);
  
  duplicates.forEach((ref, index) => {
    console.log(`Duplicate ${index + 1}:`, ref.term);
  });
  
  // Expected: References 1, 3, 5 should be marked as duplicates (Smith et al.)
  // Expected: References 2, 6 should be marked as duplicates (Johnson et al.)
  
  return {
    total: processedRefs.length,
    duplicates: duplicates.length,
    duplicateRefs: duplicates
  };
};

// Run test if this file is executed directly
if (typeof window !== 'undefined' && window.location.search.includes('test-duplicates')) {
  testDuplicateChecking();
}
