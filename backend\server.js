const express = require('express');
const cors = require('cors');
const path = require('path');
const journalMapRoutes = require('./routes/journalMap');

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'], // Add your frontend URLs
  credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Routes
app.use('/api', journalMapRoutes);

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'TE Backend API is running',
    timestamp: new Date().toISOString()
  });
});

// Serve static files from frontend build (if needed)
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, '../build')));
  
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../build', 'index.html'));
  });
}

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: err.message
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 TE Backend server running on port ${PORT}`);
  console.log(`📡 API endpoints available:`);
  console.log(`   POST /api/update-journal-map - Update journal mappings`);
  console.log(`   GET  /api/journal-map - Get current journal map`);
  console.log(`   DELETE /api/journal-map/:journal - Delete journal mapping`);
  console.log(`   GET  /api/health - Health check`);
});

module.exports = app;
