
/* Base styles for table */
.table {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0 50px 0;
  font-size: 16px;
  text-align: left;
  background-color: #f9f9f9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Table header styles */
.table th {
  background-color: #007bff;
  color: #ffffff;
  font-weight: bold;
  padding: 10px 5px;
  border: 1px solid #dddddd;
  text-align: center;
  text-transform: uppercase;
}

/* Table row styles */
.table tr {
  transition: background-color 0.3s;
}

.table tr:hover {
  background-color: #f1f1f1;
}

/* Table cell styles */
.table td {
  padding: 15px 20px 23px;
  border: 1px solid #dddddd;
  color: #333;
  word-wrap: break-word; /* Ensures long text wraps */
  position: relative;
}

td:first-child {
  padding:0;
  text-align: center;
}
.table td button{
  position: absolute;
  bottom: 5px;
  right: 5px;
}

/* Zebra stripe effect */
.table tr{
  background-color: #f2f2f2;
}

/* Responsive design for smaller screens */
@media screen and (max-width: 768px) {
  .table {
    font-size: 14px;
  }
  .table th,
  .table td {
    padding: 8px;
  }
}
/* .fixed-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: rgba(255, 255, 255, 0.8);
} */

/* .table tr[data-type="URL"] {background-color:#ffff4d} */
/* .table tr[data-type="NOT_FOUND"] {background-color:#4da9ff;position: relative;} */
/* .table tr[data-type="DUPLICATE"] {background-color:#ff4d4d} */

.table tr[data-type="NOT_FOUND"] td:nth-child(2),
.table tr[data-type="URL"] td:nth-child(2),
.table tr[data-marktype="DUPLICATE"] td:nth-child(2) {
  position: relative;
}

.table tr[data-type="URL"] td:nth-child(2)::before {
  content: 'URL';
  position: absolute;
  top: 0px;
  right: 4px;
  background-color: #0004fd;
  color: white;
  font-size: 8px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: bold;
  pointer-events: none;
}

.table tr[data-type="NOT_FOUND"] td:nth-child(2)::before {
  content: 'NOT_FOUND';
  position: absolute;
  top: 0px;
  right: 4px;
  background-color: #4da9ff;
  color: white;
  font-size: 8px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: bold;
  pointer-events: none;
}

.table tr[data-marktype="DUPLICATE"] td:nth-child(2)::before {
  content: 'DUPLICATE';
  position: absolute;
  top: 0px;
  right: 4px;
  background-color: #ff4d4d;
  color: white;
  font-size: 8px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: bold;
  pointer-events: none;
}


.loader {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(0, 0, 0, 0.1);
  border-top: 5px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.css-17vezug-marker{
  display: none !important;
}


/* td:first-child, th:first-child {
  display: none;
} */
.not-found-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 120px);
}

.not-found-card {
  background-color: #eee;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 400px;
}

.not-found-title {
  color: #ff4d4d;
  font-size: 24px;
  margin-bottom: 10px;
}

.not-found-text {
  font-size: 16px;
  color: #555;
}

.upload{
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  padding-bottom: 10px;
}


.upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20px;
  margin-bottom: 20px;

}
.upload-title {
  font-size: 24px;
  margin-bottom: 10px;
  color: #007bff;
}
.upload-input-container{
  display: flex;
  flex-direction: column;
  align-items: center;  
  width: 100%;
  height: 100px;
  border: 2px dashed #007bff;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: border-color 0.3s ease;

}
.upload-input {
  margin-top: 10px;
  margin-bottom: 10px;
  width: 76%;
}
.upload-button {
  background-color: #007bff;
  color: #fff;
  margin-top: 20px;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s ease;
}
.upload-button:hover {
  background-color: #0056b3;
}
.upload-description{
  font-size: 16px;
  color: #555;
  width: 60%;
}
.upload-area{
  width: 100%;
  height: 100px;
  border: 2px dashed #007bff;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.toggle-button {
  background-color: #007bff;
  color: #fff;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s ease;
}

.download-wrapper {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.found-citation-wrapper{
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.progress-container {
  width: 100%;
  background-color: #e5e7eb; /* Tailwind's bg-gray-200 */
  border-radius: 9999px;     /* Fully rounded */
  height: 20px;              /* Equivalent to h-2.5 (~30px) */
  margin-top: 1rem;          /* Equivalent to mt-4 */
  overflow: hidden;
}

.progress-bar {
  background-color: #43eb25; /* Tailwind's bg-blue-600 */
  height: 20px;
  border-radius: 9999px;
  transition: width 0.3s ease-in-out;
  text-align: center;
  font-weight: bolder;
  color: #f1f1f1;
}