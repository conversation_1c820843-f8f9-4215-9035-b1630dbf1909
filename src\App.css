
/* Base styles for table */
.table {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0 50px 0;
  font-size: 16px;
  text-align: left;
  background-color: #f9f9f9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Table header styles */
.table th {
  background-color: #007bff;
  color: #ffffff;
  font-weight: bold;
  padding: 10px 5px;
  border: 1px solid #dddddd;
  text-align: center;
  text-transform: uppercase;
}

/* Table row styles */
.table tr {
  transition: background-color 0.3s;
}

.table tr:hover {
  background-color: #f1f1f1;
}

/* Table cell styles */
.table td {
  padding: 15px 20px 23px;
  border: 1px solid #dddddd;
  color: #333;
  word-wrap: break-word; /* Ensures long text wraps */
  position: relative;
}

td:first-child {
  padding:0;
  text-align: center;
}
.table td button{
  position: absolute;
  bottom: 5px;
  right: 5px;
}

/* Zebra stripe effect */
.table tr{
  background-color: #f2f2f2;
}

/* Responsive design for smaller screens */
@media screen and (max-width: 768px) {
  .table {
    font-size: 14px;
  }
  .table th,
  .table td {
    padding: 8px;
  }
}
/* .fixed-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: rgba(255, 255, 255, 0.8);
} */

/* .table tr[data-type="URL"] {background-color:#ffff4d} */
/* .table tr[data-type="NOT_FOUND"] {background-color:#4da9ff;position: relative;} */
/* .table tr[data-type="DUPLICATE"] {background-color:#ff4d4d} */

.table tr[data-type="NOT_FOUND"] td:nth-child(2),
.table tr[data-type="URL"] td:nth-child(2),
.table tr[data-type="CROSSREF"] td:nth-child(2),
.table tr[data-marktype="DUPLICATE"] td:nth-child(2) {
  position: relative;
}

.table tr[data-type="URL"] td:nth-child(2)::before {
  content: 'URL';
  position: absolute;
  top: 0px;
  right: 4px;
  background-color: #0004fd;
  color: white;
  font-size: 8px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: bold;
  pointer-events: none;
}

.table tr[data-type="NOT_FOUND"] td:nth-child(2)::before {
  content: 'NOT_FOUND';
  position: absolute;
  top: 0px;
  right: 4px;
  background-color: #4da9ff;
  color: white;
  font-size: 8px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: bold;
  pointer-events: none;
}

.table tr[data-type="CROSSREF"] td:nth-child(2)::before {
  content: 'CROSSREF';
  position: absolute;
  top: 0px;
  right: 4px;
  background-color: #28a745;
  color: white;
  font-size: 8px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: bold;
  pointer-events: none;
}

.table tr[data-marktype="DUPLICATE"] td:nth-child(2)::before {
  content: 'DUPLICATE';
  position: absolute;
  top: 0px;
  right: 4px;
  background-color: #ff4d4d;
  color: white;
  font-size: 8px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: bold;
  pointer-events: none;
}


.loader {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(0, 0, 0, 0.1);
  border-top: 5px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Reference Editor Styles */
.reference-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reference-text {
  flex: 1;
}

.reference-editor {
  width: 100%;
}

.reference-textarea {
  width: 100%;
  min-height: 60px;
  padding: 8px;
  border: 2px solid #007bff;
  border-radius: 4px;
  font-family: inherit;
  font-size: 14px;
  resize: vertical;
}

.editor-buttons {
  display: flex;
  gap: 4px;
  margin-top: 4px;
}

.editor-help {
  margin-top: 4px;
  color: #666;
}

.action-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
}

.action-button:hover {
  background-color: #f0f0f0;
}

.edit-button:hover {
  background-color: #e3f2fd;
}

.save-button:hover {
  background-color: #e8f5e8;
}

.cancel-button:hover {
  background-color: #ffebee;
}

/* Quality Indicator Styles */
.quality-indicator {
  display: inline-block;
  margin-left: 8px;
}

.quality-indicator.good span {
  color: #28a745;
}

.quality-indicator .warning {
  color: #ffc107;
}

.quality-indicator .error {
  color: #dc3545;
}

/* Reference Statistics Styles */
.reference-statistics {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin: 16px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  margin-top: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background: white;
  border-radius: 4px;
  border-left: 4px solid #dee2e6;
}

.stat-label {
  font-weight: 500;
}

.stat-value {
  font-weight: bold;
}

.stat-value.pubmed {
  color: #007bff;
}

.stat-value.crossref {
  color: #28a745;
}

.stat-value.not-found {
  color: #4da9ff;
}

.stat-value.duplicates {
  color: #ff4d4d;
}

.stat-value.urls {
  color: #0004fd;
}

/* Modern Badge Components */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 2px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.status-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.badge-icon {
  font-size: 12px;
}

.badge-text {
  font-size: 10px;
  font-weight: 600;
}

/* Status Badge Variants */
.badge-pubmed {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.badge-crossref {
  background: linear-gradient(135deg, #28a745, #1e7e34);
  color: white;
}

.badge-not-found {
  background: linear-gradient(135deg, #4da9ff, #2d8cff);
  color: white;
}

.badge-url {
  background: linear-gradient(135deg, #6f42c1, #5a2d91);
  color: white;
}

.badge-duplicate {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  animation: pulse 2s infinite;
}

.badge-multiple {
  background: linear-gradient(135deg, #fd7e14, #e55a00);
  color: white;
}

/* Quality Badge Variants */
.quality-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  margin: 2px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: help;
}

.badge-excellent {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.badge-good {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
}

.badge-warning {
  background: linear-gradient(135deg, #ffc107, #e0a800);
  color: #212529;
}

.badge-poor {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
}

/* Action Badge Variants */
.action-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  border: none;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  margin: 2px;
  transition: all 0.2s ease;
  background: #f8f9fa;
  color: #495057;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.action-badge:hover:not(.disabled) {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.action-badge.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.badge-edit:hover {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.badge-copy:hover {
  background: linear-gradient(135deg, #6c757d, #545b62);
  color: white;
}

.badge-search:hover {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
}

.badge-delete:hover {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
}

.badge-save:hover {
  background: linear-gradient(135deg, #28a745, #1e7e34);
  color: white;
}

.badge-cancel:hover {
  background: linear-gradient(135deg, #6c757d, #545b62);
  color: white;
}

/* Progress Badge */
.progress-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 12px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
}

.progress-bar-container {
  width: 60px;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #28a745);
  transition: width 0.3s ease;
  border-radius: 4px;
}

.progress-text {
  font-size: 10px;
  font-weight: 600;
  color: #495057;
}

/* Pulse animation for duplicates */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* Badge container for multiple badges */
.badge-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

/* Collapsible Upload Drawer */
.upload-drawer {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 0 0 12px 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.4s ease;
  overflow: hidden;
}

.upload-drawer.collapsed {
  margin-bottom: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.upload-drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-drawer-header:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
}

.upload-drawer-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.drawer-toggle-button {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.drawer-toggle-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.upload-drawer-content {
  padding: 20px;
  background: white;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.results-section {
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Quick Upload Toggle */
.quick-upload-toggle {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.quick-toggle-button {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
  transition: all 0.3s ease;
}

.quick-toggle-button:hover {
  background: linear-gradient(135deg, #20c997, #17a2b8);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(40, 167, 69, 0.4);
}

/* Reference content layout */
.reference-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Reference Editor Improvements */
.reference-display {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.reference-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.edit-button {
  background: #007bff;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: background 0.2s ease;
}

.edit-button:hover {
  background: #0056b3;
}

.reference-main {
  flex: 1;
  min-width: 0; /* Allow text to wrap */
}

/* Improve table layout */
.table {
  table-layout: fixed;
  width: 100%;
}

.table td {
  vertical-align: top;
  padding: 12px 8px;
}

.table td:first-child {
  width: 120px; /* Fixed width for ID column */
}

.table td:nth-child(2) {
  width: 40%; /* Original reference */
}

.table td:nth-child(3) {
  width: 40%; /* Final reference */
}

/* Loading improvements */
.loading-container p {
  margin: 8px 0;
  color: #6c757d;
  font-size: 14px;
}

.reference-text {
  flex: 1;
  line-height: 1.4;
}

.reference-number {
  font-weight: 600;
  color: #495057;
  margin-right: 8px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 20px;
}

/* Remove old CSS pseudo-element styles */
.table tr[data-type="NOT_FOUND"] td:nth-child(2)::before,
.table tr[data-type="URL"] td:nth-child(2)::before,
.table tr[data-type="CROSSREF"] td:nth-child(2)::before,
.table tr[data-marktype="DUPLICATE"] td:nth-child(2)::before {
  display: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .status-badge,
  .quality-badge,
  .action-badge {
    font-size: 9px;
    padding: 3px 6px;
  }

  .badge-icon {
    font-size: 10px;
  }

  .reference-content {
    gap: 4px;
  }

  .badge-container {
    gap: 2px;
  }
}

.css-17vezug-marker{
  display: none !important;
}


/* td:first-child, th:first-child {
  display: none;
} */
.not-found-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 120px);
}

.not-found-card {
  background-color: #eee;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 400px;
}

.not-found-title {
  color: #ff4d4d;
  font-size: 24px;
  margin-bottom: 10px;
}

.not-found-text {
  font-size: 16px;
  color: #555;
}

.upload{
  display: flex;
  justify-content: space-evenly;
  align-items: flex-start;
  gap: 20px;
  padding: 0;
  margin: 0;
}

/* Responsive upload layout */
@media (max-width: 768px) {
  .upload {
    flex-direction: column;
    gap: 20px;
  }
}


.upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  flex: 1;
}
.upload-title {
  font-size: 24px;
  margin-bottom: 10px;
  color: #007bff;
}
.upload-input-container{
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100px;
  border: 2px dashed #007bff;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: border-color 0.3s ease;

}
.upload-input {
  margin-top: 10px;
  margin-bottom: 10px;
  width: 76%;
}
.upload-button {
  background-color: #007bff;
  color: #fff;
  margin-top: 20px;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s ease;
}
.upload-button:hover {
  background-color: #0056b3;
}
.upload-description{
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 16px;
  color: #555;
  flex: 1;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}
.upload-area{
  width: 100%;
  height: 100px;
  border: 2px dashed #007bff;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.toggle-button {
  background-color: #007bff;
  color: #fff;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s ease;
}

.download-wrapper {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.found-citation-wrapper{
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.progress-container {
  width: 100%;
  background-color: #e5e7eb; /* Tailwind's bg-gray-200 */
  border-radius: 9999px;     /* Fully rounded */
  height: 20px;              /* Equivalent to h-2.5 (~30px) */
  margin-top: 1rem;          /* Equivalent to mt-4 */
  overflow: hidden;
}

.progress-bar {
  background-color: #43eb25; /* Tailwind's bg-blue-600 */
  height: 20px;
  border-radius: 9999px;
  transition: width 0.3s ease-in-out;
  text-align: center;
  font-weight: bolder;
  color: #f1f1f1;
}