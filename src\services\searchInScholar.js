export const searchInScholar = async (references) => {
  const scholarArray = [];
  const notFoundSCArray = [];

    const ref = references.map(e=>e.term);
    const found = await mockScholarSearch(ref); // Replace with actual Scholar API call

    if (found && found.scholarResults){
      found.scholarResults.forEach(element => {
        const extractedData= extractCitationData(element);
        scholarArray.push({...extractedData, type:"scholar"});
      });
    }
    else{
      notFoundSCArray.push(...references);
    }
  

  return { scholarArray, notFoundSCArray };
};

function extractCitationData(obj) {
  const {actual,expected}=obj;

  if(!actual && !expected){
    return null;
  }

  
  const citation = ''; // Assuming you want to extract the first citation
  const authors = [];
  const title = "";
  const source = expected || "";
  const sortPubDate = "";
  const volumeIssuePages = `${citation.volume || ""}; ${
    citation.issue || ""
  }; ${citation.pages || ""}`;
  const elocationId = "";

  return {
    authors,
    title,
    source,
    sortPubDate,
    volumeIssuePages,
    elocationId,
  };
}

const mockScholarSearch = async (ref) => {
  // Simulate a Scholar search
const url = "http://localhost:3001/run-playwright"; // Replace with your API URL
// Making the POST request
return fetch(url, {
  method: "POST", // HTTP method
  headers: {
    "Content-Type": "application/json", // Specify JSON content type
    'Access-Control-Allow-Origin': '*'       
  },
  body: JSON.stringify({ref}), // Convert JavaScript object to JSON string
})
  .then((response) => {
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    return response.json(); // Parse the JSON response
  })
  .catch((error) => {
    console.error("Error during fetch:", error);
  });
  
};
