import React, { useState } from 'react';
import '../../App.css';

/**
 * Enhanced Reference Editor Component
 * Provides inline editing capabilities for references
 */
const ReferenceEditor = ({ reference, onSave, onCancel }) => {
  const [editedReference, setEditedReference] = useState(reference.finalStr || reference.term);
  const [isEditing, setIsEditing] = useState(false);

  const handleSave = () => {
    onSave(reference.ind, editedReference);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedReference(reference.finalStr || reference.term);
    setIsEditing(false);
    if (onCancel) onCancel();
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      handleSave();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  if (!isEditing) {
    return (
      <div className="reference-display">
        <div className="reference-text">{reference.finalStr || reference.term}</div>
        <div className="reference-actions">
          <button
            onClick={() => setIsEditing(true)}
            title="Edit reference (Click to edit)"
            className="edit-button"
          >
            ✏️ Edit
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="reference-editor">
      <textarea
        value={editedReference}
        onChange={(e) => setEditedReference(e.target.value)}
        onKeyDown={handleKeyPress}
        className="reference-textarea"
        rows={3}
        autoFocus
      />
      <div className="editor-buttons">
        <button
          onClick={handleSave}
          className="action-button save-button"
          title="Save changes (Ctrl+Enter)"
        >
          ✅
        </button>
        <button
          onClick={handleCancel}
          className="action-button cancel-button"
          title="Cancel editing (Escape)"
        >
          ❌
        </button>
      </div>
      <div className="editor-help">
        <small>Ctrl+Enter to save, Escape to cancel</small>
      </div>
    </div>
  );
};

/**
 * Reference Quality Checker Component
 * Analyzes reference quality and provides suggestions
 */
export const ReferenceQualityChecker = ({ reference }) => {
  const checkQuality = (ref) => {
    const originalText = ref.term || '';
    const finalText = ref.finalStr || ref.term || '';

    // Calculate quality based on improvements made
    const improvementScore = calculateImprovementScore(originalText, finalText);
    const structuralScore = calculateStructuralScore(finalText);
    const completenessScore = calculateCompletenessScore(finalText);

    // Weighted average of different quality aspects
    const overallScore = Math.round(
      (improvementScore * 0.4) +
      (structuralScore * 0.3) +
      (completenessScore * 0.3)
    );

    const issues = [];
    const suggestions = [];

    // Analyze issues based on scores
    if (improvementScore < 70) {
      issues.push('Significant changes needed');
      suggestions.push('Original reference required major corrections');
    }

    if (structuralScore < 70) {
      issues.push('Formatting issues');
      suggestions.push('Check punctuation and structure');
    }

    if (completenessScore < 70) {
      issues.push('Missing information');
      suggestions.push('Some required fields may be missing');
    }

    return {
      issues,
      suggestions,
      score: overallScore,
      breakdown: {
        improvement: improvementScore,
        structure: structuralScore,
        completeness: completenessScore
      }
    };
  };

  const quality = checkQuality(reference);

  const getQualityClass = (score) => {
    if (score >= 85) return 'excellent';
    if (score >= 70) return 'good';
    if (score >= 50) return 'warning';
    return 'error';
  };

  const getQualityIcon = (score) => {
    if (score >= 85) return '🌟';
    if (score >= 70) return '✅';
    if (score >= 50) return '⚠️';
    return '❌';
  };

  const qualityClass = getQualityClass(quality.score);
  const qualityIcon = getQualityIcon(quality.score);

  return (
    <div className={`quality-indicator ${qualityClass}`}>
      <span
        title={`Quality Score: ${quality.score}%\n` +
               `Improvement: ${quality.breakdown.improvement}%\n` +
               `Structure: ${quality.breakdown.structure}%\n` +
               `Completeness: ${quality.breakdown.completeness}%\n` +
               (quality.issues.length > 0 ? `\nIssues: ${quality.issues.join(', ')}\n` : '') +
               (quality.suggestions.length > 0 ? `Suggestions: ${quality.suggestions.join(', ')}` : '')
        }
        className="quality-score"
      >
        {qualityIcon} {quality.score}%
      </span>
    </div>
  );
};

// Calculate how much the reference was improved from original to final
function calculateImprovementScore(original, final) {
  if (!original || !final) return 50;

  const changes = calculateTextChanges(original, final);
  const changeRatio = changes / Math.max(original.length, final.length);

  // Less changes = higher quality (original was already good)
  // More changes = lower quality (original needed lots of work)
  if (changeRatio < 0.1) return 95; // Very few changes needed
  if (changeRatio < 0.2) return 85; // Minor changes
  if (changeRatio < 0.4) return 70; // Moderate changes
  if (changeRatio < 0.6) return 55; // Major changes
  return 40; // Extensive changes needed
}

// Calculate structural quality of the final reference
function calculateStructuralScore(text) {
  if (!text) return 0;

  let score = 100;

  // Check for proper punctuation
  if (!text.includes('.')) score -= 20;
  if (text.match(/\.{2,}|,{2,}|;{2,}/)) score -= 15; // Duplicate punctuation

  // Check for proper capitalization
  if (!text.match(/^[A-Z]/)) score -= 10; // Should start with capital

  // Check for balanced parentheses
  const openParens = (text.match(/\(/g) || []).length;
  const closeParens = (text.match(/\)/g) || []).length;
  if (openParens !== closeParens) score -= 15;

  // Check for reasonable length
  if (text.length < 30) score -= 20; // Too short
  if (text.length > 500) score -= 10; // Too long

  return Math.max(0, score);
}

// Calculate completeness of required fields
function calculateCompletenessScore(text) {
  if (!text) return 0;

  let score = 0;
  const maxScore = 100;

  // Check for authors (names with initials or full names)
  if (text.match(/[A-Z][a-z]+,?\s+[A-Z]/)) score += 25;

  // Check for year
  if (text.match(/\b(19|20)\d{2}\b/)) score += 20;

  // Check for journal/source
  if (text.match(/[A-Z][a-z]+.*[A-Z]/)) score += 20;

  // Check for volume/pages
  if (text.match(/\d+:\d+/) || text.match(/\d+-\d+/)) score += 15;

  // Check for title (sentence case with proper ending)
  if (text.match(/[A-Z][a-z].*\./)) score += 20;

  return Math.min(score, maxScore);
}

// Calculate the number of character changes between two strings
function calculateTextChanges(str1, str2) {
  if (!str1 || !str2) return Math.max(str1?.length || 0, str2?.length || 0);

  const len1 = str1.length;
  const len2 = str2.length;
  const matrix = Array(len1 + 1).fill().map(() => Array(len2 + 1).fill(0));

  // Initialize first row and column
  for (let i = 0; i <= len1; i++) matrix[i][0] = i;
  for (let j = 0; j <= len2; j++) matrix[0][j] = j;

  // Fill the matrix
  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      if (str1[i - 1] === str2[j - 1]) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,     // deletion
          matrix[i][j - 1] + 1,     // insertion
          matrix[i - 1][j - 1] + 1  // substitution
        );
      }
    }
  }

  return matrix[len1][len2];
}

/**
 * Reference Statistics Component
 * Shows statistics about the reference collection
 */
export const ReferenceStatistics = ({ references }) => {
  const stats = {
    total: references.length,
    pubmed: references.filter(r => r.type === 'FOUND').length,
    crossref: references.filter(r => r.type === 'CROSSREF').length,
    notFound: references.filter(r => r.type === 'NOT_FOUND').length,
    duplicates: references.filter(r => r.MarkType === 'DUPLICATE').length,
    urls: references.filter(r => r.type === 'URL').length,
  };

  return (
    <div className="reference-statistics">
      <h3>Reference Statistics</h3>
      <div className="stats-grid">
        <div className="stat-item">
          <span className="stat-label">Total:</span>
          <span className="stat-value">{stats.total}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">PubMed:</span>
          <span className="stat-value pubmed">{stats.pubmed}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">CrossRef:</span>
          <span className="stat-value crossref">{stats.crossref}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">Not Found:</span>
          <span className="stat-value not-found">{stats.notFound}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">Duplicates:</span>
          <span className="stat-value duplicates">{stats.duplicates}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">URLs:</span>
          <span className="stat-value urls">{stats.urls}</span>
        </div>
      </div>
    </div>
  );
};

export default ReferenceEditor;
