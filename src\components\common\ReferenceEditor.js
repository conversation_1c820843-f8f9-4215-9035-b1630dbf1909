import React, { useState } from 'react';
import '../../App.css';

/**
 * Enhanced Reference Editor Component
 * Provides inline editing capabilities for references
 */
const ReferenceEditor = ({ reference, onSave, onCancel }) => {
  const [editedReference, setEditedReference] = useState(reference.finalStr || reference.term);
  const [isEditing, setIsEditing] = useState(false);

  const handleSave = () => {
    onSave(reference.ind, editedReference);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedReference(reference.finalStr || reference.term);
    setIsEditing(false);
    if (onCancel) onCancel();
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      handleSave();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  if (!isEditing) {
    return (
      <div className="reference-display">
        <span className="reference-text">{reference.finalStr || reference.term}</span>
        <button
          onClick={() => setIsEditing(true)}
          title="Edit reference (Click to edit)"
          className="action-button edit-button"
        >
          ✏️
        </button>
      </div>
    );
  }

  return (
    <div className="reference-editor">
      <textarea
        value={editedReference}
        onChange={(e) => setEditedReference(e.target.value)}
        onKeyDown={handleKeyPress}
        className="reference-textarea"
        rows={3}
        autoFocus
      />
      <div className="editor-buttons">
        <button
          onClick={handleSave}
          className="action-button save-button"
          title="Save changes (Ctrl+Enter)"
        >
          ✅
        </button>
        <button
          onClick={handleCancel}
          className="action-button cancel-button"
          title="Cancel editing (Escape)"
        >
          ❌
        </button>
      </div>
      <div className="editor-help">
        <small>Ctrl+Enter to save, Escape to cancel</small>
      </div>
    </div>
  );
};

/**
 * Reference Quality Checker Component
 * Analyzes reference quality and provides suggestions
 */
export const ReferenceQualityChecker = ({ reference }) => {
  const checkQuality = (ref) => {
    const issues = [];
    const suggestions = [];
    const text = ref.finalStr || ref.term;

    // Check for common issues
    if (!text.includes('.')) {
      issues.push('Missing periods');
      suggestions.push('Add proper punctuation');
    }

    if (!text.match(/\d{4}/)) {
      issues.push('Missing year');
      suggestions.push('Add publication year');
    }

    if (text.length < 50) {
      issues.push('Very short reference');
      suggestions.push('May be incomplete');
    }

    if (!text.match(/[A-Z][a-z]+,?\s+[A-Z]/)) {
      issues.push('Possible author format issue');
      suggestions.push('Check author name format');
    }

    // Check for duplicate punctuation
    if (text.match(/\.{2,}|,{2,}|;{2,}/)) {
      issues.push('Duplicate punctuation');
      suggestions.push('Remove extra punctuation');
    }

    return { issues, suggestions, score: Math.max(0, 100 - (issues.length * 20)) };
  };

  const quality = checkQuality(reference);

  if (quality.issues.length === 0) {
    return (
      <div className="quality-indicator good">
        <span title="Reference quality: Good">✅ {quality.score}%</span>
      </div>
    );
  }

  return (
    <div className="quality-indicator">
      <span 
        title={`Issues: ${quality.issues.join(', ')}\nSuggestions: ${quality.suggestions.join(', ')}`}
        className={quality.score > 60 ? 'warning' : 'error'}
      >
        ⚠️ {quality.score}%
      </span>
    </div>
  );
};

/**
 * Reference Statistics Component
 * Shows statistics about the reference collection
 */
export const ReferenceStatistics = ({ references }) => {
  const stats = {
    total: references.length,
    pubmed: references.filter(r => r.type === 'FOUND').length,
    crossref: references.filter(r => r.type === 'CROSSREF').length,
    notFound: references.filter(r => r.type === 'NOT_FOUND').length,
    duplicates: references.filter(r => r.MarkType === 'DUPLICATE').length,
    urls: references.filter(r => r.type === 'URL').length,
  };

  return (
    <div className="reference-statistics">
      <h3>Reference Statistics</h3>
      <div className="stats-grid">
        <div className="stat-item">
          <span className="stat-label">Total:</span>
          <span className="stat-value">{stats.total}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">PubMed:</span>
          <span className="stat-value pubmed">{stats.pubmed}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">CrossRef:</span>
          <span className="stat-value crossref">{stats.crossref}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">Not Found:</span>
          <span className="stat-value not-found">{stats.notFound}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">Duplicates:</span>
          <span className="stat-value duplicates">{stats.duplicates}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">URLs:</span>
          <span className="stat-value urls">{stats.urls}</span>
        </div>
      </div>
    </div>
  );
};

export default ReferenceEditor;
