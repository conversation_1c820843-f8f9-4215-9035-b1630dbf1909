import { api<PERSON><PERSON>, db, retmax, retmode } from "../constants/env";
import { esearch, esummary } from "../constants/urls";
import {
  delayedFetch,
  abbreviatePageNumbers,
  customEndsWith,
  capitalizeAfterColon,
} from "./styling";
import { searchInGenAI } from "./fetchFromGenAI";

export const removeDOIUrls = (references) => {
  return references.map((ref) => ({
    ...ref,
    genTerm: ref.term
      .replace(/\s*https?:\/\/(?:dx\.)?doi\.org\/\S+$/i, "")
      .trim(),
  }));
};

export const searchInPubMed = async (references) => {
  const pubMedArray = [];
  const notFoundArray = [];
  const multipleFound = [];
  const withoutDOIRef = removeDOIUrls(references);
  const { genAIArray } = await searchInGenAI(withoutDOIRef);
  console.log("genAIArray", genAIArray);
  
  for (const ref of genAIArray) {
    try {
      // First attempt with searchStr
      const firstSearchResult = await mockPubMedSearch(ref.searchStr);
      let found = firstSearchResult;
      let extractedData = extractCitationDetails(firstSearchResult);
      let isMultipleArticles = firstSearchResult?.result?.uids?.length > 1;

      // Only try second search if first one definitely returned no results
      if ((!firstSearchResult || !extractedData) && ref.searchStr2) {
        const secondSearchResult = await mockPubMedSearch(ref.searchStr2);
        found = secondSearchResult;
        extractedData = extractCitationDetails(secondSearchResult);
        isMultipleArticles = secondSearchResult?.result?.uids?.length > 1;
      }

      if (found && extractedData) {
        pubMedArray.push({ ...ref, ...extractedData, type: "FOUND", found });
      } else if (isMultipleArticles) {
        multipleFound.push({
          ...ref,
          ...extractedData,
          type: "MULTIPLE_PubMed",
          found,
        });
      } else {
        notFoundArray.push(ref);
      }
    } catch (error) {
      console.error("Error in PubMed search:", error);
      notFoundArray.push(ref);
    }
  }
  return { pubMedArray, notFoundArray, multipleFound };
};

// Mock functions for demonstration purposes
const mockPubMedSearch = async (term) => {
  // Simulate a PubMed search
  const searchTerm = term
    .trim()
    .replaceAll("&amp;", "")
    .replaceAll("&", "")
    .replace(/\s+/g, "+");
  let searchUrl = `${esearch}?db=${db}&term=${encodeURI(
    searchTerm
  )}&retmode=${retmode}&retmax=${retmax}&api_key=${apiKey}`;

  console.log(searchUrl, "URL");
  const searchResponse = await delayedFetch(searchUrl);
  if (!searchResponse.ok) throw new Error("Search API failed");
  const searchResult = await searchResponse.json();

  if (searchResult.esearchresult.idlist.length > 0) {
    const pmids = searchResult.esearchresult.idlist.join(",");
    const summaryUrl = `${esummary}?db=${db}&id=${pmids}&retmode=${retmode}&api_key=${apiKey}`;

    const summaryResponse = await delayedFetch(summaryUrl);
    if (!summaryResponse.ok) throw new Error("Summary API failed");

    return await summaryResponse.json();
  }
};

export const createNewArrayWithoutAutors = (array) => {
  const newArray = [];
  for (const element of array) {
    const newTitle = element.term.split(".").slice(1).join(".").trim();
    newArray.push({ ...element, term: newTitle });
  }
  return newArray;
};

const extractCitationDetails = (data) => {
  // Ensure the data structure is as expected
  if (
    !data ||
    !data.result ||
    !data.result.uids ||
    data.result.uids.length === 0
  ) {
    return null;
  }

  const uid = data.result.uids[0]; // Get the first UID
  const article = data.result[uid]; // Get the article data

  if (!article) {
    return null;
  }

  // Extract the required fields
  let authors = article.authors.map((author) => author.name).join(", ");

  if (article.authors.length > 6) {
    authors = `${article.authors
      .map((author) => author.name)
      .slice(0, 6)
      .join(", ")}, et al.`;
  }

  if (!customEndsWith(authors, ".")) {
    authors += ".";
  }
  let title = article.title;

  if (!customEndsWith(title, ".")) {
    title += ".";
  }
  title = capitalizeAfterColon(title);
  const journalName = article.source;
  const year = article.sortpubdate?.split("/")[0];
  const page = abbreviatePageNumbers(article?.pages || "");
  const volume = article.volume;
  const issue = article.issue.toLowerCase();
  const vol = issue.includes("sup")
    ? volume + "(" + article.issue + ")"
    : volume;

  // Return the extracted data as an object
  return {
    finalStr: `${authors} ${title || ""} ${journalName || ""} ${
      year ? year + ";" : ""
    }${vol}${page ? ":" + page : ""}.`,
  };
};
