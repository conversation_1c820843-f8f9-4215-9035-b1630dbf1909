import { api<PERSON><PERSON>, db, retmax, retmode } from "../constants/env";
import { esearch, esummary } from "../constants/urls";
import {
  delayedFetch,
  abbreviatePageNumbers,
  customEndsWith,
  capitalizeAfterColon,
} from "./styling";
import { searchInGenAI } from "./fetchFromGenAI";

export const removeDOIUrls = (references) => {
  return references.map((ref) => ({
    ...ref,
    genTerm: ref.term
      .replace(/\s*https?:\/\/(?:dx\.)?doi\.org\/\S+$/i, "")
      .trim(),
  }));
};

export const searchInPubMed = async (references) => {
  const pubMedArray = [];
  const notFoundArray = [];
  const multipleFound = [];
  const withoutDOIRef = removeDOIUrls(references);
  const { genAIArray } = await searchInGenAI(withoutDOIRef);
  console.log("genAIArray", genAIArray);

  for (const ref of genAIArray) {
    try {
      console.log("Processing reference:", ref.ind + 1);
      console.log("Original term:", ref.term);
      console.log("Extracted authors:", ref.extractedData?.authors);

      // Strategy 1: Search with structured data
      let searchResult = null;
      let extractedData = null;
      let searchStrategy = "";

      if (ref.searchStr) {
        console.log("Strategy 1 - Structured data:", ref.searchStr);
        searchResult = await mockPubMedSearch(ref.searchStr);
        if (searchResult?.result?.uids?.length > 0) {
          extractedData = selectBestMatch(searchResult, ref);
          if (extractedData) {
            searchStrategy = "structured_data_all_authors";
          }
        }
      }

      // Strategy 2: Title + Journal + First Author (if strategy 1 failed)
      if (!extractedData && ref.searchStr2) {
        console.log("Strategy 2 - Title + Journal + First Author:", ref.searchStr2);
        searchResult = await mockPubMedSearch(ref.searchStr2);
        if (searchResult?.result?.uids?.length > 0) {
          extractedData = selectBestMatch(searchResult, ref);
          if (extractedData) {
            searchStrategy = "title_journal_first_author";
          }
        }
      }

      // Strategy 3: Title + Journal only (if strategy 2 failed)
      if (!extractedData && ref.searchStr3) {
        console.log("Strategy 3 - Title + Journal only:", ref.searchStr3);
        searchResult = await mockPubMedSearch(ref.searchStr3);
        if (searchResult?.result?.uids?.length > 0) {
          extractedData = selectBestMatch(searchResult, ref);
          if (extractedData) {
            searchStrategy = "title_journal_only";
          }
        }
      }

      if (extractedData) {
        console.log(`Found match using ${searchStrategy} strategy`);
        pubMedArray.push({
          ...ref,
          ...extractedData,
          type: "FOUND",
          found: searchResult,
          searchStrategy
        });
      } else if (searchResult?.result?.uids?.length > 1) {
        console.log("Multiple results found, but no good match");
        multipleFound.push({
          ...ref,
          type: "MULTIPLE_PubMed",
          found: searchResult,
        });
      } else {
        console.log("No results found in PubMed");
        notFoundArray.push(ref);
      }
    } catch (error) {
      console.error("Error in PubMed search:", error);
      notFoundArray.push(ref);
    }
  }
  return { pubMedArray, notFoundArray, multipleFound };
};

// Function to select the best match from multiple PubMed results
function selectBestMatch(searchResult, originalRef) {
  if (!searchResult?.result?.uids || searchResult.result.uids.length === 0) {
    return null;
  }

  const uids = searchResult.result.uids;
  console.log(`Found ${uids.length} potential matches`);

  // If only one result, use it
  if (uids.length === 1) {
    return extractCitationDetails(searchResult);
  }

  // Multiple results - find the best match
  let bestMatch = null;
  let bestScore = 0;

  for (const uid of uids) {
    const article = searchResult.result[uid];
    if (!article) continue;

    let score = 0;

    // Check year match (highest priority)
    const extractedYear = originalRef.extractedData?.year;
    const articleYear = article.sortpubdate?.split("/")[0];

    if (extractedYear && articleYear && extractedYear === articleYear) {
      score += 50; // High score for year match
      console.log(`Year match found: ${extractedYear} = ${articleYear}`);
    }

    // Check title similarity
    const originalTitle = originalRef.extractedData?.article_title?.toLowerCase() || "";
    const articleTitle = article.title?.toLowerCase() || "";

    if (originalTitle && articleTitle) {
      const titleSimilarity = calculateTitleSimilarity(originalTitle, articleTitle);
      score += titleSimilarity * 30; // Up to 30 points for title similarity
      console.log(`Title similarity: ${titleSimilarity.toFixed(2)} (Original: "${originalTitle}" vs Article: "${articleTitle}")`);
    }

    // Check journal similarity
    const originalJournal = originalRef.extractedData?.journal_title?.toLowerCase() || "";
    const articleJournal = article.source?.toLowerCase() || "";

    if (originalJournal && articleJournal) {
      if (articleJournal.includes(originalJournal) || originalJournal.includes(articleJournal)) {
        score += 20; // Journal match
        console.log("Journal match found");
      }
    }

    console.log(`Article ${uid} score: ${score}`);

    if (score > bestScore) {
      bestScore = score;
      bestMatch = { uid, article };
    }
  }

  // Return best match if we have reasonable confidence (score > 20) or fallback to first result
  if (bestMatch && bestScore > 20) {
    console.log(`Selected best match with score: ${bestScore}`);
    // Create a modified search result with only the best match
    const modifiedResult = {
      ...searchResult,
      result: {
        ...searchResult.result,
        uids: [bestMatch.uid]
      }
    };
    return extractCitationDetails(modifiedResult);
  }

  // Fallback: if no good match found, use the first result (most recent/relevant)
  console.log("No good match found, using first result as fallback");
  const firstUid = uids[0];
  const modifiedResult = {
    ...searchResult,
    result: {
      ...searchResult.result,
      uids: [firstUid]
    }
  };
  return extractCitationDetails(modifiedResult);
}

// Calculate title similarity between two titles
function calculateTitleSimilarity(title1, title2) {
  if (!title1 || !title2) return 0;

  // Normalize titles - handle hyphen variations better
  const normalize = (str) => {
    return str
      .toLowerCase()
      // Normalize hyphens: "type-a" and "type a" should match
      .replace(/[-\s]+/g, ' ')
      .replace(/[^\w\s]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()
      .split(' ')
      .filter(word => word.length > 2);
  };

  const words1 = normalize(title1);
  const words2 = normalize(title2);

  if (words1.length === 0 || words2.length === 0) return 0;

  // Calculate Jaccard similarity
  const set1 = new Set(words1);
  const set2 = new Set(words2);

  const intersection = new Set([...set1].filter(x => set2.has(x)));
  const union = new Set([...set1, ...set2]);

  return intersection.size / union.size;
}

// Mock functions for demonstration purposes
const mockPubMedSearch = async (term) => {
  // Validate input
  if (!term || typeof term !== 'string') {
    console.warn("Invalid search term:", term);
    return null;
  }

  // Simulate a PubMed search
  const cleanedTerm = term
    .trim()
    .replaceAll("&amp;", "")
    .replaceAll("&", "");

  // Use encodeURIComponent for proper URL encoding instead of manual + replacement
  const encodedTerm = encodeURIComponent(cleanedTerm);
  let searchUrl = `${esearch}?db=${db}&term=${encodedTerm}&retmode=${retmode}&retmax=${retmax}&api_key=${apiKey}`;

  console.log(searchUrl, "URL");
  const searchResponse = await delayedFetch(searchUrl);
  if (!searchResponse.ok) throw new Error("Search API failed");
  const searchResult = await searchResponse.json();

  if (searchResult.esearchresult.idlist.length > 0) {
    const pmids = searchResult.esearchresult.idlist.join(",");
    const summaryUrl = `${esummary}?db=${db}&id=${pmids}&retmode=${retmode}&api_key=${apiKey}`;

    const summaryResponse = await delayedFetch(summaryUrl);
    if (!summaryResponse.ok) throw new Error("Summary API failed");

    return await summaryResponse.json();
  }
};

export const createNewArrayWithoutAutors = (array) => {
  const newArray = [];
  for (const element of array) {
    const newTitle = element.term.split(".").slice(1).join(".").trim();
    newArray.push({ ...element, term: newTitle });
  }
  return newArray;
};

const extractCitationDetails = (data) => {
  // Ensure the data structure is as expected
  if (
    !data ||
    !data.result ||
    !data.result.uids ||
    data.result.uids.length === 0
  ) {
    return null;
  }

  const uid = data.result.uids[0]; // Get the first UID
  const article = data.result[uid]; // Get the article data

  if (!article) {
    return null;
  }

  // Extract the required fields
  let authors = article.authors.map((author) => author.name).join(", ");

  if (article.authors.length > 6) {
    authors = `${article.authors
      .map((author) => author.name)
      .slice(0, 6)
      .join(", ")}, et al.`;
  }

  if (!customEndsWith(authors, ".")) {
    authors += ".";
  }
  let title = article.title;

  if (!customEndsWith(title, ".")) {
    title += ".";
  }
  title = capitalizeAfterColon(title);
  const journalName = article.source;
  const year = article.sortpubdate?.split("/")[0];
  const page = abbreviatePageNumbers(article?.pages || "");
  const volume = article.volume;
  const issue = article.issue.toLowerCase();
  const vol = issue.includes("sup")
    ? volume + "(" + article.issue + ")"
    : volume;

  // Return the extracted data as an object
  return {
    finalStr: `${authors} ${title || ""} ${journalName || ""} ${
      year ? year + ";" : ""
    }${vol}${page ? ":" + page : ""}.`,
  };
};
