import React from 'react';
import '../../App.css';

/**
 * Component to display when no references are found
 * @param {Object} props - Component props
 * @param {string} props.message - Optional custom message
 * @returns {JSX.Element} - Rendered component
 */
const NotFoundReferences = ({ message }) => {
  return (
    <div className="not-found-container">
      <div className="not-found-card">
        <h2 className="not-found-title">References Not Found</h2>
        <p className="not-found-text">
          {message || "We couldn't find any references in the file. Please check the document or upload a different one."}
        </p>
      </div>
    </div>
  );
};

export default NotFoundReferences;
